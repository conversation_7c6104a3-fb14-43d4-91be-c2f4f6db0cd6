## 2 OBMCQ建模方法2

随着钻井作业向深水、高温高压、复杂地层（如易水化膨胀的页岩）以及大位移井和水平井等更具挑战性的领域拓展，油基泥浆（Oil-Based Mud, OBM）因其优异的润滑性、高温稳定性、强地层抑制性以及潜在的提高钻速能力而被广泛应用。尽管OBM在钻井工程方面具有显著优势，但其独特的物理化学性质给地层评价，尤其是依赖电学和电磁学原理的先进测井技术带来了严峻挑战。对于NMR测井来说，对受OBM影响的T2​谱进行形态校正，或者采用不依赖标准BFV划分的渗透率估算方法，对于在OBM环境下获得可靠的渗透率至关重要。

对于巴西桑托斯深水碳酸盐岩地层来，通过对比LWD测井和WL测井可以发现：
- 随钻深浅电阻率数据值接近，电缆浅电阻率明显大于深电阻率。
- LWD NMR孔隙度与岩心孔隙度一致性较好，WL NMR孔隙度略大于LWD NMR。
- OBM侵入造成WL NMR的T2谱形态和位置均发生改变，LWD NMR的单峰变为WL NMR的双峰，T2谱整体位置向长T2方向移动，特别是大孔隙发育的层段（渗透率更高），移动程度更明显。

通过于岩心数据对比，发现岩心渗透率与DT2_P50和DPHIT_NMR有一定的相关性，相关程度DT2_P50更明显一些，渗透率越高DT2_P50就越大。因此OBMCQ的目标是训练一个机器学习模型来预测DT2_P50和DPHIT_NMR，用作FOSTER-NMR中油基泥浆校正因子的输入。

注意：OBM侵入的影响还有时间因素，由于数据有限，因此暂未考虑时间因素，目前只是考虑了储层特征对于OBM侵入的反映。

### 2.1 建模数据

- 仅C-1井和T-1井同时具备LWD NMR和WL NMR，因此选这两口井的数据参与建模。
- 输入曲线仅是表格数据：
  - DEN, CN, DT, RD_LOG10, RS_LOG10, DRES
  - T2LM_LOG10, T2LM_LONG_LOG10, T2_P50_LOG10, T2_P20_LOG10
  - PHIT_NMR, PHIE_NMR, BFV_NMR, BVI_NMR, FFV_NMR, SWB_NMR, SWI_NMR, SFF_NMR
  - VMICRO, VMESO, VMACRO, SMICRO, SMESO, SMACRO
  - LT2STDDEV_FFI, LSKEW_FFI, LKURT_FFI, SDR_PROXY
- 标签曲线：DT2_P50, DPHIT_NMR。



### 2.2 建模流程

#### 2.2.1 符号与定义

* $\mathcal{D}$: 完整的建模数据集，包含来自 C-1 和 T-1井的所有样本。
* $W$: 井的集合，$W = \{W_1, W_2\}$，其中 $W_1$ 代表 C-1 井的数据， $W_2$ 代表 T-1井的数据。
* $\mathbf{X}$: 特征矩阵，其行代表深度样本点，列代表测井曲线特征。
* $\mathbf{y}$: 目标（标签）向量。由于存在两个预测目标，我们分别定义为 $\mathbf{y}_{DT2\_P50}$ 和 $\mathbf{y}_{DPHIT\_NMR}$。本方案将为每个目标独立训练模型。
* $\mathcal{M}$: 候选模型算法的集合，$\mathcal{M} = \{\text{XGBoost, RandomForest, SVM}\}$。
* $m$: $\mathcal{M}$ 中的一个具体算法，例如 $m = \text{XGBoost}$。
* $\Lambda_m$: 算法 $m$ 的超参数空间。
* $\lambda$: 算法 $m$ 的一组特定超参数配置，$\lambda \in \Lambda_m$。
* $f(\mathbf{X}; \lambda)$: 使用算法 $m$ 和超参数 $\lambda$ 在特征集 $\mathbf{X}$ 上训练得到的模型。
* $E(f, \mathcal{D}_{test})$: 在测试集 $\mathcal{D}_{test}$ 上评估模型 $f$ 性能的函数。本方案主要使用均方根误差 (Root Mean Squared Error, RMSE) 作为评估指标，其定义如下（公式2-1）：
    $$
    \text{RMSE} = \sqrt{\frac{1}{N} \sum_{i=1}^{N} (y_i - \hat{y}_i)^2}
    $$
    其中，$N$ 为测试样本数，$y_i$ 为真值，$\hat{y}_i$ 为模型预测值。

#### 2.2.2 数据准备与特征工程

1.  **数据加载**: 解析 `obmcq_input.wp.xlsx` 文件，加载 C-1 和 T-1井的输入曲线和标签曲线。将井号 `WELL_NO` 作为后续数据划分的组标签 (group label)。
2.  **特征变换**:
    * 根据文档，输入特征包含二维曲线 `PHI_T2_DIST_CUM`。设该曲线的第二维为 $N_{bins}$，则需将其展开为 $N_{bins}$ 个独立的一维特征。
        $$
        \text{PHI\_T2\_DIST\_CUM} \rightarrow \{\text{PHI\_T2\_DIST\_CUM[1], ..., PHI\_T2\_DIST\_CUM[}N_{bins}\text{]}\}
        $$
    * 初始全量特征集 $\mathbf{X}_{full}$ 由文档中指定的 15 条一维曲线和 $N_{bins}$ 条展开后的二维曲线元素共同构成。
3.  **缺失值处理 (集成于交叉验证流程中)**: 为防止数据泄露，缺失值填充将在交叉验证的每个折叠（fold）内部独立进行。具体而言，将仅使用**当前折叠的训练数据**计算中位数，然后用该中位数来填充该**训练集和对应的验证集/测试集**。这确保了测试集的信息在任何阶段都不会影响到训练过程。

#### 2.2.3 嵌套交叉验证框架 (Nested Cross-Validation)

核心是双层循环的嵌套交叉验证，其**唯一目的**是为不同建模流程提供无偏的泛化能力估计，以便从中选出最优的算法组合。

##### 2.2.3.1 外层循环: 留一井交叉验证 (LOWO-CV)

* **目的**: 提供对模型最终泛化能力的无偏估计。
* **执行**: 外层循环共进行 $F = |W| = 2$ 折 (fold)。在第 $i$ 折中 ($i \in \{1, 2\}$)：
    * **测试集**: $\mathcal{D}_{test}^{(i)} = W_i$
    * **训练集**: $\mathcal{D}_{train}^{(i)} = \mathcal{D} \setminus W_i$
* 在每一折中，训练集 $\mathcal{D}_{train}^{(i)}$ 将被送入内层循环进行模型优化。

##### 2.2.3.2 内层循环: K-Fold 交叉验证

* **目的**: 在给定的训练集 $\mathcal{D}_{train}^{(i)}$ 上，执行稳健的特征选择和超参数寻优。
* **执行**: 设定 $K=5$。将 $\mathcal{D}_{train}^{(i)}$ 划分为 $K$ 个互不相交的子集。

#### 2.2.4 内层优化: 特征选择

此步骤在内层循环中，为每一折外层训练集 $\mathcal{D}_{train}^{(i)}$ 独立进行。

1.  **方法**: 采用**递归特征消除交叉验证 (Recursive Feature Elimination with Cross-Validation, RFECV)**。该方法能自动找到最优的特征数量，比固定阈值法更为稳健。
2.  **流程**:
    a. 在 $\mathcal{D}_{train}^{(i)}$ 上，初始化一个RFECV对象。
    b. **基础评估器**: RFECV内部使用一个带默认参数的RandomForest回归模型来评估特征的重要性。
    c. **交叉验证**: RFECV利用内层循环的K-Fold划分来评估不同数量的特征子集所产生的模型性能。
    d. **最优子集选择**: RFECV会自动选择在交叉验证中表现最佳的特征数量，并返回对应的最优特征子集 $\mathbf{X}_{opt}^{(i)}$。

#### 2.2.5 内层优化: 超参数寻优

此步骤紧随特征选择之后，使用由RFECV筛选出的特征集 $\mathbf{X}_{opt}^{(i)}$。

1.  **方法**: 针对每种算法 $m \in \mathcal{M}$，采用**随机搜索交叉验证 (Randomized Search CV)** 在超参数空间 $\Lambda_m$ 中寻找最优配置。
2.  **流程**:
    a. 随机搜索的评估过程利用内层循环的 K-Fold 划分。
    b. 对于每一个候选超参数组合 $\lambda$，在 $K$ 个内部训练集上分别训练模型，并在对应的内部验证集上评估性能，计算平均验证误差 $\bar{E}_{val}(\lambda)$。
    c. 寻找使平均验证误差最小化的超参数组合 $\lambda_{opt}^{(i, m)}$：
       $$
       \lambda_{opt}^{(i, m)} = \arg\min_{\lambda \in \Lambda_m} \left( \frac{1}{K} \sum_{k=1}^{K} E(f(\mathbf{X}_{inner\_train}^{(k)}; \lambda), \mathcal{D}_{inner\_val}^{(k)}) \right)
       $$

#### 2.2.6 外层评估: 模型性能计算

1.  **模型重训练**: 使用完整的 $\mathcal{D}_{train}^{(i)}$ 数据、最优特征集 $\mathbf{X}_{opt}^{(i)}$ 和最优超参数 $\lambda_{opt}^{(i, m)}$，训练得到最终的折内模型 $f_{opt}^{(i, m)}$。
2.  **性能评估**: 使用外层循环预留的测试集 $\mathcal{D}_{test}^{(i)}$ 评估该模型，记录其性能，例如 $RMSE^{(i, m)}$。
    $$
    RMSE^{(i, m)} = E(f_{opt}^{(i, m)}, \mathcal{D}_{test}^{(i)})
    $$
此过程将在外层循环中对每一折、每一种算法重复执行。

#### 2.2.7 模型比较与融合策略选择

外层循环全部完成后，我们得到一个包含所有算法在所有折上性能的评估矩阵。

1.  **计算平均性能**: 对每种算法 $m$，计算其在所有 $F$ 折上的平均性能和标准差。
    $$
    \overline{\text{RMSE}}_m = \frac{1}{F} \sum_{i=1}^{F} \text{RMSE}^{(i, m)}
    $$
2.  **选择加权融合模型**: 考虑到数据样本量小，为提高模型的**稳健性 (Robustness)** 和泛化能力，我们采用**加权模型融合 (Weighted Model Ensembling)** 策略。
    * **候选模型**: 选取在嵌套交叉验证中表现最好的两个算法（例如，平均RMSE最低的两个），记为 $m_1^*$ 和 $m_2^*$。
    * **权重计算**: 权重与模型的性能（此处用1/RMSE表示）成正比。这使得表现更好的模型在最终预测中占有更大话语权。
        * 计算各模型的性能得分: $S_1 = 1 / \overline{\text{RMSE}}_{m_1^*}$, $S_2 = 1 / \overline{\text{RMSE}}_{m_2^*}$
        * 归一化得到权重: $w_1 = S_1 / (S_1 + S_2)$, $w_2 = S_2 / (S_1 + S_2)$
    * **融合方式**: 最终模型的预测值将是 $m_1^*$ 和 $m_2^*$ 两个模型预测值的**加权平均**。
        $$
        \hat{y}_{ensemble} = w_1 \cdot \hat{y}_{m_1^*} + w_2 \cdot \hat{y}_{m_2^*}
        $$
    这种策略相比简单平均，能更充分地利用交叉验证得到的性能信息。

#### 2.2.8 最终模型训练与交付

嵌套交叉验证的核心目标是**无偏地评估并选择出最优的建模流程**（此处为“采用RFECV并加权融合XGBoost和RandomForest”的流程）。此评估环节至此结束。接下来的步骤将聚焦于利用**所有可用数据**，通过已选定的最优流程来训练一个用于实际部署的、性能最强的最终模型。

1.  **确定最优算法组合及权重**:
    * 根据2.2.7节中嵌套交叉验证得出的结果，确定表现最佳的两种算法（$m_1^*$ 和 $m_2^*$）以及它们各自的融合权重（$w_1, w_2$）。

2.  **在全量数据上为每个模型组件进行最终优化**:
    * **目标**: 为每个选定的算法（$m_1^*$ 和 $m_2^*$），在**完整的、全部的数据集 $\mathcal{D}$** 上，应用之前在嵌套交叉验证**内层循环**中使用的优化流程（RFECV + RandomizedSearch），以找到一组全局最优的配置。
    * **方法**: 此处不再进行任何形式的井间交叉验证。而是将整个优化流程视为一个独立的函数，输入为数据集，输出为最优模型。
    * **执行**:
        * **针对算法 $m_1^*$**:
            a.  **最终特征选择**: 在**全部数据集 $\mathcal{D}$** 上，运行**RFECV**（内部使用`GroupKFold`按井号分组进行交叉验证），筛选出算法 $m_1^*$ 的最终特征集 $\mathbf{X}_{final,1}$。
            b.  **最终超参数寻优**: 使用特征集 $\mathbf{X}_{final,1}$，在**全部数据集 $\mathcal{D}$** 上，通过**带分组（井号）的随机搜索交叉验证 (`RandomizedSearchCV` with `GroupKFold`)**，为算法 $m_1^*$ 找到最终的超参数 $\lambda_{final,1}$。
        * **针对算法 $m_2^*$**:
            a.  对算法 $m_2^*$ 重复上述完全相同的过程，得到其最终特征集 $\mathbf{X}_{final,2}$ 和超参数 $\lambda_{final,2}$。

3.  **最终模型训练与交付**:
    * **全量训练**:
        * 使用算法 $m_1^*$、最终特征集 $\mathbf{X}_{final,1}$ 和最终超参数 $\lambda_{final,1}$，在**全部可用数据集 $\mathcal{D}$** 上进行训练，得到最终训练好的模型 $f_{final,1}$。
        * 使用算法 $m_2^*$、最终特征集 $\mathbf{X}_{final,2}$ 和最终超参数 $\lambda_{final,2}$，同样在**全部数据集 $\mathcal{D}$** 上训练，得到 $f_{final,2}$。
    * **模型交付**: 由于存在 `DT2_P50` 和 `DPHIT_NMR` 两个独立的预测目标，上述第2步和第3步将为每个目标独立执行。最终，产出并保存两组模型文件，并将其封装为一个**加权融合模型 (Weighted Ensemble Model)**，该模型在调用时会自动使用步骤1中确定的权重（$w_1, w_2$）进行预测。
        * $f_{ensemble, \text{DT2\_P50}} = \text{Ensemble}(f_{final,1,\text{DT2\_P50}}, f_{final,2,\text{DT2\_P50}}, \text{weights}=[w_1, w_2])$
        * $f_{ensemble, \text{DPHIT\_NMR}} = \text{Ensemble}(f_{final,1,\text{DPHIT\_NMR}}, f_{final,2,\text{DPHIT\_NMR}}, \text{weights}=[w_1, w_2])$
    这两个融合模型即为OBMCQ阶段的最终交付成果，它们是在利用了所有可用信息后得到的最优版本。

### 2.3 结果输出

完成Stage-1的建模后，应整理并输出以下核心结果，以确保工作的透明性、可复现性，并准确传达模型性能。

1.  **最终融合模型**:
    * 为 `DT2_P50` 和 `DPHIT_NMR` 两个目标分别交付最终的融合模型文件。模型应被封装，使其在调用时能自动完成对内部两个子模型预测结果的平均。
    * 附带一份 `JSON` 或 `YAML` 格式的配置文件，清晰列出构成每个融合模型的具体算法（如 `XGBoost` 和 `RandomForest`）及其最终训练时所使用的全部超参数。

2.  **模型性能评估报告**:
    * 提供一张表格，汇总在嵌套交叉验证（LOWO-CV）阶段，各候选算法（XGBoost, RandomForest, SVM）在两折测试中的 `RMSE` 值、平均 `RMSE` 值以及 `RMSE` 的标准差。

3.  **不确定性说明 (Uncertainty Statement)**:
    * **必须在报告中以显著方式强调以下内容**：
        > “本次建模的数据源仅包含 **C-1** 和 **T-1** 两口井。因此，用于评估模型泛化能力的留一井交叉验证（LOWO-CV）仅包含两折。基于两个样本点计算得出的平均性能指标（如 $\overline{\text{RMSE}}$），虽然是统计意义上的无偏估计，但其**方差较大，存在较高的不确定性**。这意味着，当前评估的性能分数是对模型在未来新井上表现的一个初步估计，实际应用效果可能与此存在偏差。采用模型融合策略旨在提升模型的稳健性以部分缓解此问题，但使用者仍需对该结论的统计局限性有充分认知。”

### 2.4 OBMCQ伪代码

#### 1\. 主控流程函数 (`run_obmcq_workflow`)

**目的**：定义整个OBMCQ建模任务的端到端执行顺序。

```python
import pandas as pd
# 假设其他辅助函数和类已定义
# from .nested_cv import run_nested_cv
# from .final_training import train_final_ensemble_model
# from .utils import load_data, save_model

def run_obmcq_workflow(config: dict):
    """
    执行完整的OBMCQ建模工作流。

    Args:
        config (dict): 包含所有配置信息的字典，如文件路径、模型参数等。
    """
    # --- 步骤 1: 加载和准备数据 ---
    # 此处省略了详细的wp文件解析逻辑，假设有一个工具函数
    print("正在加载数据...")
    full_dataset = load_data(config['data_filepath'])
    # 分离特征和两个目标变量
    X = full_dataset[config['features']]
    y_dt2_p50 = full_dataset['DT2_P50']
    y_dphit_nmr = full_dataset['DPHIT_NMR']
    groups = full_dataset['WELL_NO'] # 用于GroupKFold的井号分组

    # --- 步骤 2: 为每个目标变量独立执行建模 ---
    final_models = {}
    for target_name, y_target in [('DT2_P50', y_dt2_p50), ('DPHIT_NMR', y_dphit_nmr)]:
        print(f"--- 开始为目标 '{target_name}' 进行建模 ---")

        # 2a. 执行嵌套交叉验证以评估并选择最佳模型流程
        # 这个函数会返回最佳的两个模型名称及其融合权重
        print("执行嵌套交叉验证以评估和选择模型...")
        nested_cv_results = run_nested_cv(
            X=X,
            y=y_target,
            groups=groups,
            config=config['nested_cv_config']
        )
        
        # 2b. 使用所有数据和CV结果来训练最终的融合模型
        print("训练最终的融合模型...")
        final_ensemble_model = train_final_ensemble_model(
            X=X,
            y=y_target,
            groups=groups,
            nested_cv_results=nested_cv_results, # 传入CV的结果
            config=config['final_training_config']
        )

        # --- 步骤 3: 保存最终模型 ---
        print(f"保存目标 '{target_name}' 的最终模型...")
        save_path = f"{config['output_path']}/final_model_{target_name}.pkl"
        final_ensemble_model.save(save_path) # 模型自带保存方法
        final_models[target_name] = final_ensemble_model
        print(f"--- 目标 '{target_name}' 建模完成 ---")

    return final_models
```

#### 2\. 嵌套交叉验证函数 (`run_nested_cv`) 


```python
from sklearn.model_selection import LeaveOneGroupOut, KFold
from sklearn.pipeline import Pipeline
from sklearn.impute import SimpleImputer
from sklearn.feature_selection import RFECV
from sklearn.model_selection import RandomizedSearchCV
import numpy as np

def run_nested_cv(X, y, groups, config):
    """
    执行嵌套交叉验证流程 (已修正)。
    严格遵循先RFECV选择特征，再用选定特征进行RandomizedSearch调优的顺序。

    Returns:
        dict: 包含最佳模型名称和权重的字典。
    """
    lowo_cv = LeaveOneGroupOut()
    all_model_scores = {model_name: [] for model_name in config['models_to_evaluate']}

    # --- 外层循环 (LOWO) ---
    for fold, (train_idx, test_idx) in enumerate(lowo_cv.split(X, y, groups)):
        X_train, X_test = X.iloc[train_idx], X.iloc[test_idx]
        y_train, y_test = y.iloc[train_idx], y.iloc[test_idx]
        
        print(f"  外层折叠 {fold+1}/{lowo_cv.get_n_splits(groups=groups)}: 验证井 {groups.iloc[test_idx].unique()[0]}")

        for model_name, model_config in config['models_to_evaluate'].items():
            
            # --- 内层优化步骤1: 递归特征消除 (RFECV) ---
            # 创建一个包含预处理和评估器的流水线，供RFECV内部使用
            # 这可以确保在RFECV的每一次内部CV折叠中，imputer都只对训练部分进行fit
            rfe_base_estimator = model_config['base_estimator_for_rfe']
            rfe_pipeline = Pipeline([
                ('imputer', SimpleImputer(strategy='median')),
                ('estimator', rfe_base_estimator)
            ])
            
            # 使用内层K-Fold进行特征数量选择
            inner_cv = KFold(n_splits=config['inner_cv_folds'], shuffle=True, random_state=config['random_seed'])
            feature_selector = RFECV(
                estimator=rfe_pipeline,
                step=1,
                cv=inner_cv,
                scoring='neg_root_mean_squared_error',
                n_jobs=-1
            )
            
            # 在当前外层训练集上执行特征选择
            feature_selector.fit(X_train, y_train)
            
            # 使用训练好的选择器来转换数据
            X_train_selected = feature_selector.transform(X_train)
            X_test_selected = feature_selector.transform(X_test)
            
            # --- 内层优化步骤2: 超参数寻优 (RandomizedSearchCV) ---
            # 现在，在已经选定的特征上进行超参数搜索
            # 注意：由于数据在特征选择后已无缺失值，此处的Imputer主要用于处理原始数据中可能存在的、
            # 在RFECV的某些内部折叠中未出现但在最终训练中出现的缺失值，是一种安全措施。
            # 更简洁的方式是先对X_train填充一次，然后进行后续操作。
            # 但为保持流程的严谨性（严格在训练集上fit），将其放入Pipeline。
            search_pipeline = Pipeline([
                ('imputer', SimpleImputer(strategy='median')),
                ('model', model_config['estimator'])
            ])

            param_grid = {f'model__{k}': v for k, v in model_config['param_space'].items()}

            random_search = RandomizedSearchCV(
                estimator=search_pipeline,
                param_distributions=param_grid,
                n_iter=config['n_iter_random_search'],
                cv=inner_cv, # 同样使用内层K-Fold
                scoring='neg_root_mean_squared_error',
                n_jobs=-1,
                random_state=config['random_seed']
            )
            # 在被选择出的特征上进行超参数搜索
            random_search.fit(X_train_selected, y_train)
            
            # --- 外层评估 ---
            best_model_for_fold = random_search.best_estimator_
            y_pred = best_model_for_fold.predict(X_test_selected)
            rmse_score = np.sqrt(np.mean((y_test - y_pred)**2))
            
            all_model_scores[model_name].append(rmse_score)
            print(f"    - 模型 '{model_name}': {len(feature_selector.get_support(indices=True))} 个特征, RMSE = {rmse_score:.4f}")

    # --- 循环结束后: 计算平均性能并选择最佳模型 ---
    avg_scores = {name: np.mean(scores) for name, scores in all_model_scores.items()}
    sorted_models = sorted(avg_scores.items(), key=lambda item: item[1])
    m1_star_name, m1_star_rmse = sorted_models[0]
    m2_star_name, m2_star_rmse = sorted_models[1]

    s1 = 1 / m1_star_rmse
    s2 = 1 / m2_star_rmse
    w1 = s1 / (s1 + s2)
    w2 = s2 / (s1 + s2)
    
    return {
        'best_models': [
            {'name': m1_star_name, 'weight': w1},
            {'name': m2_star_name, 'weight': w2}
        ],
        'avg_scores': avg_scores,
        'all_scores': all_model_scores
    }
```

#### 3\. 最终模型训练函数 (`train_final_ensemble_model`) 


```python
from sklearn.model_selection import GroupKFold
from sklearn.base import BaseEstimator, TransformerMixin

# 定义一个辅助类，用于在最终的流水线中应用特征选择的结果
class FeatureSelectorByMask(BaseEstimator, TransformerMixin):
    def __init__(self, mask):
        self.mask = mask
    def fit(self, X, y=None):
        return self
    def transform(self, X):
        # 确保列名一致性
        return X.loc[:, self.mask]

def train_final_ensemble_model(X, y, groups, nested_cv_results, config):
    """
    训练并返回最终的、可部署的加权融合模型 (已修正)。
    此函数严格遵循2.2.8节的规定，在全量数据上为每个模型组件独立地、
    无泄漏地寻找最优特征和超参数，然后进行最终训练。
    """
    trained_sub_models = []
    best_models_config = nested_cv_results['best_models']
    
    # 定义按井号分组的CV策略，用于所有最终优化步骤
    group_cv = GroupKFold(n_splits=config['n_splits_group_cv'])

    # --- 为每个选出的最佳模型组件独立进行最终优化和训练 ---
    for model_info in best_models_config:
        model_name = model_info['name']
        model_config = config['models_to_evaluate'][model_name]
        print(f"  正在为最终模型组件 '{model_name}' 进行优化...")

        # --- 步骤 1: 在全量数据上进行最终特征选择 (RFECV) ---
        # 使用GroupKFold确保在寻找最优特征数量时没有井间信息泄露
        print(f"    > 步骤1: 使用GroupKFold进行最终特征选择...")
        rfe_estimator = model_config['base_estimator_for_rfe']
        # 流水线确保imputer在CV的每个训练折叠上独立fit
        fs_pipeline = Pipeline([
            ('imputer', SimpleImputer(strategy='median')),
            ('estimator', rfe_estimator)
        ])
        final_feature_selector = RFECV(
            estimator=fs_pipeline,
            cv=group_cv,
            scoring='neg_root_mean_squared_error',
            n_jobs=-1
        )
        final_feature_selector.fit(X, y, groups=groups)
        final_feature_mask = final_feature_selector.get_support()
        X_final_selected = X.loc[:, final_feature_mask]
        print(f"    > '{model_name}' 选定 {X_final_selected.shape[1]} 个最终特征。")
        
        # --- 步骤 2: 在选定的特征上进行最终超参数寻优 (RandomizedSearchCV) ---
        print(f"    > 步骤2: 使用GroupKFold进行最终超参数寻优...")
        search_estimator = Pipeline([
            ('imputer', SimpleImputer(strategy='median')),
            ('model', model_config['estimator'])
        ])
        param_grid = {f'model__{k}': v for k, v in model_config['param_space'].items()}
        
        final_search = RandomizedSearchCV(
            estimator=search_estimator,
            param_distributions=param_grid,
            n_iter=config['n_iter_random_search'],
            cv=group_cv,
            scoring='neg_root_mean_squared_error',
            n_jobs=-1,
            random_state=config['random_seed']
        )
        final_search.fit(X_final_selected, y, groups=groups)
        best_params = final_search.best_params_
        # 将流水线参数名转换回模型参数名
        final_model_params = {k.replace('model__', ''): v for k, v in best_params.items()}

        # --- 步骤 3: 使用最优配置，在所有数据上训练最终的可部署模型组件 ---
        print(f"    > 步骤3: 训练最终的可部署模型组件...")
        final_model_component = model_config['estimator'].set_params(**final_model_params)
        
        # 创建最终的可部署流水线，它将依次执行：缺失值填充 -> 特征选择 -> 建模
        deployable_pipeline = Pipeline([
            ('imputer', SimpleImputer(strategy='median')),
            ('selector', FeatureSelectorByMask(mask=final_feature_mask)),
            ('model', final_model_component)
        ])
        
        # 在全部数据上训练这个最终的流水线
        deployable_pipeline.fit(X, y)
        trained_sub_models.append(deployable_pipeline)

    # --- 组装成最终的加权融合模型 ---
    final_ensemble = WeightedEnsembleModel(
        model_1=trained_sub_models[0],
        model_2=trained_sub_models[1],
        weight_1=best_models_config[0]['weight'],
        weight_2=best_models_config[1]['weight']
    )
    
    return final_ensemble
```

#### 4\. 加权融合模型类 (`WeightedEnsembleModel`)

```python
import joblib

class WeightedEnsembleModel:
    """
    一个自定义类，用于封装加权融合模型。
    """
    def __init__(self, model_1=None, model_2=None, weight_1=None, weight_2=None):
        self.model_1 = model_1
        self.model_2 = model_2
        self.weight_1 = weight_1
        self.weight_2 = weight_2
        # 保存组件的模型名称和权重，便于追溯
        self.config = {
            'model_1_type': type(model_1).__name__ if model_1 else None,
            'model_2_type': type(model_2).__name__ if model_2 else None,
            'weight_1': self.weight_1,
            'weight_2': self.weight_2
        }

    def predict(self, X):
        """
        使用加权平均法进行预测。
        """
        if not all([self.model_1, self.model_2, self.weight_1 is not None, self.weight_2 is not None]):
            raise RuntimeError("模型或权重未被初始化或加载！")

        pred_1 = self.model_1.predict(X)
        pred_2 = self.model_2.predict(X)
        
        weighted_prediction = (pred_1 * self.weight_1) + (pred_2 * self.weight_2)
        
        return weighted_prediction

    def save(self, filepath: str):
        """
        将整个融合模型对象保存到单个文件。
        """
        joblib.dump(self, filepath)
        print(f"融合模型已保存至 '{filepath}'")

    @staticmethod
    def load(filepath: str):
        """
        从文件中加载融合模型。
        """
        model = joblib.load(filepath)
        print(f"融合模型已从 '{filepath}' 加载")
        return model
```