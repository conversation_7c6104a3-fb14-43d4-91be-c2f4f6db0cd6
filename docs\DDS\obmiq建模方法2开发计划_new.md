# `scape.core.obmiq_baselines` 组件开发计划与设计说明书

**版本: 1.0**

## 1. 目标与定位

### 1.1. 项目目标

开发一个位于 `scape.core.obmiq_baselines` 的新组件，作为**可追踪机器学习组件开发框架**的另一个标准实现。该组件的核心任务与 `scape.core.obmiq` 相同：基于测井数据，预测油基泥浆侵入导致的 `DT2_P50` 和 `DPHIT_NMR` 变化。

**本开发计划的所有技术实现细节，均严格遵循《SCAPE_方法说明书_OBMIQ建模方法2.md》中定义的算法、架构与规程。**

### 1.2. 架构定位

*   **框架遵从性**: `obmiq_baselines` 将严格遵循《SCAPE_组件开发最佳实践.md》，实现为一个包含`训练`和`预测`两个步骤（Steps）的**多步骤包**。
*   **技术栈**: 采用 `Scikit-learn` 作为核心框架，构建包含特征选择、超参数寻优和模型融合的 `Pipeline`。核心模型将包括 `XGBoost`, `RandomForest`, `SVM` 等。
*   **可复现性**: 整个流程，从嵌套交叉验证到最终模型训练，都将被`RunContext`追踪，确保结果的完全可复现。

---

## 2. 核心设计决策与“干跑”分析

本节是开发前的核心“干跑”分析，旨在预演关键技术点，统一设计决策。

### 2.1. 遵循多步骤包架构

我们将采用标准的多步骤包结构，将训练和预测逻辑分离到不同的门面（Facade）中，同时共享内部逻辑和配置。

*   `training_facade.py`: 实现 `run_obmiq_baselines_training_step()`，负责执行嵌套交叉验证、模型选择和最终模型训练。
*   `prediction_facade.py`: 实现 `run_obmiq_baselines_prediction_step()`，负责使用已训练的融合模型进行预测。
*   `config.py`: 定义 `ObmiqBaselinesTrainingConfig` 和 `ObmiqBaselinesPredictionConfig`。
*   `constants.py`: 定义所有产物和绘图模板的常量。
*   `artifact_handler.py`: 定义一个共享的 `ObmiqBaselinesArtifactHandler`。
*   `internal/`: 包含共享的内部实现。

### 2.2. Facade接口设计

Facade 接口将遵循框架的“关注点分离”原则，只接收必要的输入。

#### **训练步骤 Facade 签名**

```python
# training_facade.py
def run_obmiq_baselines_training_step(
    config: ObmiqBaselinesTrainingConfig,
    ctx: RunContext,
    train_bundle: WpDataFrameBundle,
    *,
    tabular_features: list[str],
    target_features: list[str],
    grouping_feature: str
) -> dict[str, Any]:
    # ...
```

#### **预测步骤 Facade 签名**

```python
# prediction_facade.py
def run_obmiq_baselines_prediction_step(
    config: ObmiqBaselinesPredictionConfig,
    ctx: RunContext,
    model_assets: dict[str, Any],
    prediction_bundle: WpDataFrameBundle,
    *,
    output_curve_names: tuple[str, str]
) -> dict[str, Any]:
    # ...
```

### 2.3. 核心产物设计 (模型资产包)

训练步骤的核心产出是一个**“模型资产包” (Model Assets)**，它将包含 `scikit-learn` 特有的产物。

**模型资产包结构 (将被 `ObmiqBaselinesArtifactHandler` 序列化/反序列化):**

```python
# 一个Python字典，可以被joblib保存
model_assets = {
    "ensemble_model": ...,  # 最终训练好的WeightedEnsembleModel对象
    "metadata": {
        "tabular_features_ordered": ['GR', 'DEN', ...], # 必须保留顺序
        "target_features_ordered": ['DT2_P50', 'DPHIT_NMR'],
        "ensemble_config": {
            "model_1": {"name": "XGBoost", "weight": 0.6},
            "model_2": {"name": "RandomForest", "weight": 0.4}
        }
    }
}
```
`ensemble_model` 内部将包含完整的 `scikit-learn` `Pipeline`，每个`Pipeline`都封装了`Imputer -> FeatureSelector -> Model`的完整流程，确保预测时的一致性。

### 2.4. 训练工作流内部规程 (Internal Procedures)

`training_facade`的逻辑将被清晰地划分为内部规程。

1.  **模型定义规程 (`internal/model_definitions.py`)**:
    *   **职责**: 集中定义所有候选模型（XGBoost, RandomForest, SVM）的 `scikit-learn` 评估器实例及其对应的超参数搜索空间（`param_distributions`）。

2.  **嵌套交叉验证规程 (`internal/nested_cv_procedure.py`)**:
    *   **职责**: 实现 `run_nested_cv()` 函数，执行完整的嵌套交叉验证流程，以无偏地评估各候选模型的泛化能力。
    *   **流程**:
        *   外层循环: `LeaveOneGroupOut` (LOWO) 按井划分。
        *   内层循环: 在外层训练集上，对每个候选模型（如XGBoost）执行 `RandomizedSearchCV` 进行超参数寻优。
        *   **防泄漏核心**: `RandomizedSearchCV` 的 `estimator` 参数**必须**是一个 `scikit-learn` 的 `Pipeline` 对象。该 `Pipeline` 封装了 `Imputer -> RFECV -> Model` 的完整流程。`RandomizedSearchCV` 的搜索空间将同时包含 `RFECV` 和 `Model` 的超参数。这确保了在超参数寻优的每一次内部交叉验证中，特征选择都是在训练折叠上重新执行的，从而避免了数据泄露。
    *   **输出**: 返回一个包含所有模型性能的报告，并确定最佳的两个模型及其融合权重。

3.  **最终模型训练规程 (`internal/final_training_procedure.py`)**:
    *   **职责**: 实现 `train_final_ensemble_model()` 函数。
    *   **流程**:
        *   接收嵌套CV的结果（最佳模型和权重）。
        *   对每个选定的最佳模型，在**全部数据**上重新执行特征选择（`RFECV` with `GroupKFold`）和超参数寻优（`RandomizedSearchCV` with `GroupKFold`），以找到全局最优配置。
        *   使用最优配置在**全部数据**上训练最终的模型组件。
        *   将训练好的模型组件封装进 `WeightedEnsembleModel`。
    *   **输出**: 包含最终融合模型的**“模型资产包”**。

---

## 3. 拟定目录与文件结构

```
scape/core/obmiq_baselines/
├── __init__.py
├── README.md
├── training_facade.py
├── prediction_facade.py
├── config.py
├── constants.py
├── artifact_handler.py
├── plotting.py
├── plot_profiles.py
└── internal/
    ├── __init__.py
    ├── model_definitions.py      # 定义Scikit-learn模型和超参数空间
    ├── nested_cv_procedure.py    # 嵌套交叉验证规程
    ├── final_training_procedure.py # 最终模型训练规程
    └── ensemble_model.py         # WeightedEnsembleModel类的实现
```

---

## 4. API 与核心组件定义 (代码骨架)

### `config.py`

```python
from pydantic import BaseModel, Field, PositiveInt

class ObmiqBaselinesTrainingConfig(BaseModel):
    """OBMIQ Baselines 训练步骤的流程控制配置。"""
    random_seed: int = Field(2025, description="全局随机种子")

    # Nested CV
    inner_cv_folds: PositiveInt = Field(5, description="内层交叉验证的折数")

    # Randomized Search
    n_iter_random_search: PositiveInt = Field(
        50, description="随机搜索的迭代次数"
    )

class ObmiqBaselinesPredictionConfig(BaseModel):
    """预测步骤配置，目前为空。"""
    pass
```

### `constants.py`

```python
from enum import Enum


class ObmiqBaselinesTrainingArtifacts(str, Enum):
    MODEL_ASSETS = "obmiq_baselines.models.assets_sklearn"
    TRAINING_CONFIG = "obmiq_baselines.configs.training_config"
    NESTED_CV_REPORT = "obmiq_baselines.reports.nested_cv_performance"
    FINAL_MODEL_EVALUATION_DATA = "obmiq_baselines.data_snapshots.final_model_evaluation"
    EVAL_CROSSPLOT = "obmiq_baselines.plots.eval_crossplot"
    EVAL_RESIDUALS_PLOT = "obmiq_baselines.plots.eval_residuals_plot"
    EVAL_RESIDUALS_HIST = "obmiq_baselines.plots.eval_residuals_hist"
    FEATURE_IMPORTANCE_DATA = "obmiq_baselines.data_snapshots.feature_importance"
    FEATURE_IMPORTANCE_PLOT = "obmiq_baselines.plots.feature_importance"
    # ... 其他评估图表常量 ...

class ObmiqBaselinesPredictionArtifacts(str, Enum):
    PREDICTIONS = "obmiq_baselines.datasets.predictions"
    # ... 其他评估图表常量 ...
```

### `internal/ensemble_model.py`

```python
import joblib

class WeightedEnsembleModel:
    """一个自定义类，用于封装加权融合模型。"""
    def __init__(self, model_1, model_2, weight_1, weight_2):
        self.model_1 = model_1
        self.model_2 = model_2
        self.weight_1 = weight_1
        self.weight_2 = weight_2

    def predict(self, X):
        pred_1 = self.model_1.predict(X)
        pred_2 = self.model_2.predict(X)
        return (pred_1 * self.weight_1) + (pred_2 * self.weight_2)

    def save(self, filepath: str):
        joblib.dump(self, filepath)

    @staticmethod
    def load(filepath: str):
        return joblib.load(filepath)
```

### `training_facade.py`

```python
def run_obmiq_baselines_training_step(...):
    """执行OBMIQ Baselines模型训练、评估和交付。"""
    # 1. 为每个目标独立建模
    for target_name in target_features:
        # 2. 内部规程一：嵌套交叉验证
        nested_cv_results = nested_cv_procedure.run_nested_cv(...)

        # 3. 内部规程二：最终模型训练
        model_assets = final_training_procedure.train_final_ensemble_model(...)

        # 4. 保存核心产物
        handler.save_model_assets(model_assets, ...)
        ctx.register_artifact(...)

    return {"status": "completed"}
```

---

## 5. 开发步骤与任务分解 (小步快跑策略)

### **里程碑 1: 基础架构与模型定义 (1-2天)**

*   **任务 1.1: 项目骨架搭建**: 创建 `scape/core/obmiq_baselines` 目录及所有必需的空文件。
*   **任务 1.2: 定义配置与常量**: 实现 `config.py` 和 `constants.py`。
*   **任务 1.3: 实现融合模型类**: 在 `internal/ensemble_model.py` 中实现 `WeightedEnsembleModel`。
*   **任务 1.4: 定义候选模型**: 在 `internal/model_definitions.py` 中定义 `scikit-learn` 模型及其超参数搜索空间。

---

### **里程碑 2: 核心规程实现 (3-4天)**

*   **任务 2.1: 实现嵌套交叉验证规程 (`internal/nested_cv_procedure.py`)**
    *   **核心挑战**: 正确实现双层循环，并使用 `Pipeline` 将 `RFECV` 和 `Model` 封装起来，传递给 `RandomizedSearchCV`，以从机制上防止数据泄露。
    *   **验证**: 编写集成测试，使用少量数据和2个候选模型，验证流程能跑通并返回正确的性能报告结构。

*   **任务 2.2: 实现最终模型训练规程 (`internal/final_training_procedure.py`)**
    *   **核心挑战**: 正确地在全量数据上，使用带 `GroupKFold` 的 `RFECV` 和 `RandomizedSearchCV` 来找到最终配置。
    *   **验证**: 单元测试验证该函数能正确构建并训练最终的融合模型，并返回结构正确的资产包字典。

---

### **里程碑 3: 组装与集成 (2-3天)**

*   **任务 3.1: 组装训练门面 (`training_facade.py`)**
    *   **职责**: 编排对 `nested_cv_procedure` 和 `final_training_procedure` 的调用，并处理产物的保存与注册。
    *   **验证**: 编写集成测试，使用 `MagicMock` 模拟 `RunContext`，验证 `facade` 的调用流程。

*   **任务 3.2: 实现产物处理器和预测门面**
    *   **职责**: 实现 `artifact_handler.py` 和 `prediction_facade.py`。
    *   **验证**: 单元测试验证模型资产包的保存和加载。集成测试验证预测步骤能正确加载模型并输出结果。

*   **任务 3.3: 端到端工作流测试**
    *   **职责**: 编写一个最终的端到端集成测试，按顺序调用训练和预测 `facade`。
    *   **验证**: 断言预测步骤能成功运行，并验证所有产物文件都已正确生成。

---

### **里程碑 4: 文档与收尾 (1天)**

*   **任务 4.1: 编写组件 `README.md`**: 撰写详细的组件使用说明文档。
*   **任务 4.2: 代码审查与最终清理**: 确保代码符合所有规范，注释清晰，测试覆盖全面。

---

## 6. 风险评估与应对

1.  **流程复杂性**: 嵌套交叉验证和最终训练流程涉及多个步骤，容易出错，特别是数据泄露。
    *   **应对**: 严格使用 `scikit-learn` 的 `Pipeline` 对象来封装 `Imputer`, `FeatureSelector`, `Model` 等步骤，从机制上防止数据泄露。编写详尽的测试用例。
2.  **计算性能**: `RFECV` 和 `RandomizedSearchCV` 的计算成本可能很高。
    *   **应对**: 在所有可并行的步骤中（如 `RFECV`, `RandomizedSearchCV`）设置 `n_jobs=-1`。在开发和测试阶段使用较小的 `n_iter`。
3.  **依赖管理**: 需要引入 `xgboost`, `scikit-learn` 等库。
    *   **应对**: 在 `pyproject.toml` 中清晰地定义所有依赖及其版本。
