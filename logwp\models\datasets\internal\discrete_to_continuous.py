#!/usr/bin/env python3
"""离散型数据集转连续型数据集服务模块。

提供WpDiscreteDataset转换为WpContinuousDataset的核心转换逻辑。

Architecture
------------
层次/依赖: datasets/service层，离散数据重采样业务逻辑
设计原则: Service Layer模式、向量化插值、类型安全
性能特征: 高效插值处理、内存优化、批量处理

Examples:
    >>> # 基本转换
    >>> df, metadata, depth_range = convert_discrete_to_continuous(
    ...     discrete_dataset, sampling_interval=0.5
    ... )

    >>> # 自定义深度范围转换
    >>> df, metadata, depth_range = convert_discrete_to_continuous(
    ...     discrete_dataset,
    ...     sampling_interval=1.0,
    ...     depth_range=(2490.0, 2520.0),
    ...     interpolation_method="linear",
    ...     out_of_range_fill_value=-999
    ... )

References:
    《SCAPE_DDS_logwp_离散转连续数据集.md》§3 - 转换算法设计
"""

from __future__ import annotations

import copy
from datetime import datetime
from typing import TYPE_CHECKING, Any

import numpy as np
import pandas as pd

from logwp.models.constants import WpCurveCategory, WpDepthRole, WpStandardColumn
from logwp.models.curve.metadata import CurveBasicAttributes, CurveMetadata
from logwp.models.exceptions import WpDataError, WpValidationError
from logwp.infra.exceptions import ErrorContext
from logwp.infra import get_logger

if TYPE_CHECKING:
    from logwp.models.datasets.discrete import WpDiscreteDataset

logger = get_logger(__name__)


def convert_discrete_to_continuous(
    discrete_dataset: "WpDiscreteDataset",
    sampling_interval: float,
    *,
    depth_range: tuple[float, float] | None = None,
    interpolation_method: str = "nearest",
    out_of_range_fill_value: Any = np.nan
) -> tuple[pd.DataFrame, CurveMetadata, tuple[float, float]]:
    """将离散型数据集转换为连续型数据集格式。

    实现离散数据到连续数据的重采样转换，支持多种插值方法和自定义深度范围。

    Args:
        discrete_dataset: 源离散型数据集
        sampling_interval: 深度采样间隔（必须 > 0）
        depth_range: 输出深度范围，None表示使用原数据集范围
        interpolation_method: 默认插值方法，可被曲线属性覆盖
        out_of_range_fill_value: 超出原数据集范围的填充值

    Returns:
        tuple[pd.DataFrame, CurveMetadata, tuple[float, float]]:
            (转换后的DataFrame, 新的曲线元数据, 实际深度范围)

    Raises:
        WpValidationError: 当参数验证失败时抛出
        WpDataError: 当数据集状态异常时抛出

    Note:
        - 转换后的深度曲线名称为WpStandardColumn.DEPTH
        - 保留原有的井名曲线
        - 使用pandas插值方法进行数据重采样
        - 深度范围会按采样间隔对齐
        - 插值方法优先级：强制方法 > 数据类型约束 > 用户指定方法

    Examples:
        >>> # 基本转换
        >>> df, metadata, depth_range = convert_discrete_to_continuous(
        ...     discrete_dataset, sampling_interval=0.5
        ... )
        >>> assert WpStandardColumn.DEPTH in df.columns
        >>> assert len(df) >= 2  # 至少有起始和结束点

        >>> # 自定义范围转换
        >>> df, metadata, depth_range = convert_discrete_to_continuous(
        ...     discrete_dataset,
        ...     sampling_interval=1.0,
        ...     depth_range=(2490.0, 2520.0),
        ...     interpolation_method="linear",
        ...     out_of_range_fill_value=-999
        ... )

    References:
        《SCAPE_DDS_logwp_离散转连续数据集.md》§3.1 - 深度序列生成
        《SCAPE_DDS_logwp_离散转连续数据集.md》§3.2 - 插值策略设计
    """
    logger.info(
        "开始离散型数据集转连续型数据集转换",
        operation="convert_discrete_to_continuous",
        dataset_name=str(discrete_dataset.name),
        sampling_interval=sampling_interval,
        custom_depth_range=depth_range is not None,
        interpolation_method=interpolation_method,
        out_of_range_fill_value=str(out_of_range_fill_value)
    )

    # 1. 参数验证
    _validate_conversion_parameters(discrete_dataset, sampling_interval, depth_range, interpolation_method)

    # 2. 获取源数据集信息
    source_df = discrete_dataset.df
    source_metadata = discrete_dataset.curve_metadata

    # 3. 获取深度和井名曲线信息
    depth_curve = discrete_dataset.get_single_depth_reference_curve()
    well_curves = source_metadata.get_well_identifier_curves()

    # 4. 按井分别处理或使用指定深度范围
    if depth_range is not None:
        # 用户指定了深度范围，使用传统逻辑
        target_depth_range = depth_range
        depth_sequence = _generate_depth_sequence(target_depth_range, sampling_interval)
        actual_depth_range = (depth_sequence[0], depth_sequence[-1])

        new_df = _create_continuous_dataframe_with_fixed_range(
            source_df, depth_sequence, depth_curve, well_curves,
            source_metadata, interpolation_method, target_depth_range, out_of_range_fill_value
        )
    else:
        # 按井分别处理，避免跨井的无意义深度范围
        new_df, actual_depth_range = _create_continuous_dataframe_per_well(
            source_df, depth_curve, well_curves, source_metadata,
            sampling_interval, interpolation_method, out_of_range_fill_value
        )

    # 7. 创建新的曲线元数据
    new_metadata = _create_continuous_metadata(
        source_metadata, depth_curve, sampling_interval
    )

    logger.info(
        "离散型数据集转连续型数据集转换完成",
        operation="convert_discrete_to_continuous",
        dataset_name=str(discrete_dataset.name),
        original_rows=len(source_df),
        converted_rows=len(new_df),
        actual_depth_range=actual_depth_range
    )

    return new_df, new_metadata, actual_depth_range


def _validate_conversion_parameters(
    discrete_dataset: "WpDiscreteDataset",
    sampling_interval: float,
    depth_range: tuple[float, float] | None,
    interpolation_method: str
) -> None:
    """验证转换参数的有效性。

    Args:
        discrete_dataset: 离散型数据集
        sampling_interval: 深度采样间隔
        depth_range: 深度范围
        interpolation_method: 插值方法

    Raises:
        WpValidationError: 当参数无效时抛出
        WpDataError: 当数据集状态异常时抛出
    """
    # 验证采样间隔
    if sampling_interval <= 0:
        raise WpValidationError(
            "深度采样间隔必须为正数",
            context=ErrorContext(
                operation="validate_conversion_parameters",
                dataset_name=str(discrete_dataset.name),
                additional_info={
                    "sampling_interval": sampling_interval,
                    "requirement": "sampling_interval > 0"
                }
            )
        )

    # 验证数据集状态
    if discrete_dataset.df.empty:
        raise WpDataError(
            "离散型数据集为空，无法进行转换",
            context=ErrorContext(
                operation="validate_conversion_parameters",
                dataset_name=str(discrete_dataset.name),
                additional_info={"reason": "empty_dataset"}
            )
        )

    # 验证数据点数量（至少需要2个点进行插值）
    if len(discrete_dataset.df) < 2:
        raise WpDataError(
            "离散型数据集数据点不足，至少需要2个数据点进行插值",
            context=ErrorContext(
                operation="validate_conversion_parameters",
                dataset_name=str(discrete_dataset.name),
                additional_info={
                    "current_points": len(discrete_dataset.df),
                    "minimum_required": 2
                }
            )
        )

    # 验证深度曲线
    try:
        discrete_dataset.get_single_depth_reference_curve()
    except WpDataError as e:
        raise WpDataError(
            "离散型数据集缺少有效的深度参考曲线",
            context=ErrorContext(
                operation="validate_conversion_parameters",
                dataset_name=str(discrete_dataset.name),
                additional_info={"original_error": str(e)}
            )
        ) from e

    # 验证深度范围
    if depth_range is not None:
        if len(depth_range) != 2 or depth_range[0] >= depth_range[1]:
            raise WpValidationError(
                "深度范围格式无效",
                context=ErrorContext(
                    operation="validate_conversion_parameters",
                    dataset_name=str(discrete_dataset.name),
                    additional_info={
                        "depth_range": depth_range,
                        "requirement": "tuple[float, float] with start < end"
                    }
                )
            )

        # 验证深度范围是否包含原数据集范围（按井验证）
        wells_ranges = discrete_dataset.get_wells_depth_ranges()
        for well, well_range in wells_ranges.items():
            if (depth_range[0] > well_range[0] or depth_range[1] < well_range[1]):
                raise WpValidationError(
                    f"指定的深度范围必须包含所有井的深度范围，井{well}超出范围",
                    context=ErrorContext(
                        operation="validate_conversion_parameters",
                        dataset_name=str(discrete_dataset.name),
                        additional_info={
                            "specified_range": depth_range,
                            "well_range": well_range,
                            "well_name": well,
                            "requirement": "specified_range must contain all wells' ranges"
                    }
                )
            )

    # 验证插值方法（基本验证，具体方法由pandas验证）
    if not isinstance(interpolation_method, str) or not interpolation_method.strip():
        raise WpValidationError(
            "插值方法必须为非空字符串",
            context=ErrorContext(
                operation="validate_conversion_parameters",
                dataset_name=str(discrete_dataset.name),
                additional_info={
                    "interpolation_method": interpolation_method,
                    "requirement": "non-empty string"
                }
            )
        )


def _generate_depth_sequence(
    depth_range: tuple[float, float],
    sampling_interval: float
) -> np.ndarray:
    """生成对齐的深度序列。

    Args:
        depth_range: 深度范围
        sampling_interval: 采样间隔

    Returns:
        np.ndarray: 对齐的深度序列
    """
    depth_start, depth_end = depth_range

    # 深度边界对齐
    aligned_start = np.floor(depth_start / sampling_interval) * sampling_interval
    aligned_end = np.ceil(depth_end / sampling_interval) * sampling_interval

    # 生成深度序列
    depth_sequence = np.arange(aligned_start, aligned_end + sampling_interval, sampling_interval)

    logger.debug(
        "深度序列生成完成",
        operation="generate_depth_sequence",
        original_range=depth_range,
        aligned_range=(aligned_start, aligned_end),
        sampling_interval=sampling_interval,
        depth_points=len(depth_sequence)
    )

    return depth_sequence


def _create_continuous_dataframe(
    source_df: pd.DataFrame,
    depth_sequence: np.ndarray,
    depth_curve: CurveBasicAttributes,
    well_curves: list[str],
    curve_metadata: CurveMetadata,
    default_interpolation_method: str,
    original_depth_range: tuple[float, float],
    out_of_range_fill_value: Any
) -> pd.DataFrame:
    """创建连续型数据集的DataFrame。

    🚨 **重要修复**：现在支持多井数据的正确处理。

    修复说明：
    - 检测输入数据是否包含多口井
    - 单井数据：使用原有的高效逻辑
    - 多井数据：按井分组处理，确保所有井的数据都被正确保留
    - 支持部分曲线为空的情况（保留NaN值）

    Args:
        source_df: 源离散型数据集的DataFrame
        depth_sequence: 深度序列
        depth_curve: 深度曲线属性
        well_curves: 井名曲线列表
        curve_metadata: 曲线元数据
        default_interpolation_method: 默认插值方法
        original_depth_range: 原数据集深度范围
        out_of_range_fill_value: 超出原数据集范围的填充值

    Returns:
        pd.DataFrame: 转换后的连续型DataFrame
    """
    # 🚨 关键修复：检测并正确处理多井数据
    #
    # 修复逻辑：
    # 1. 检查是否包含多口井的数据
    # 2. 单井数据：使用原有高效逻辑
    # 3. 多井数据：按井分组处理，确保所有井数据都被保留

    # 检测井的数量
    unique_wells = []
    if well_curves and well_curves[0] in source_df.columns:
        unique_wells = source_df[well_curves[0]].dropna().unique().tolist()

    depth_column = depth_curve.dataframe_column_name

    if len(unique_wells) <= 1:
        # 🟢 单井数据：使用原有的高效逻辑
        logger.debug(
            "单井数据转换",
            operation="create_continuous_dataframe",
            wells_count=len(unique_wells),
            well_name=unique_wells[0] if unique_wells else "unknown"
        )
        return _create_single_well_continuous_dataframe(
            source_df, depth_sequence, depth_curve, well_curves,
            curve_metadata, default_interpolation_method, original_depth_range, out_of_range_fill_value
        )
    else:
        # 🔧 多井数据：按井分组处理，然后合并结果
        logger.debug(
            "多井数据转换",
            operation="create_continuous_dataframe",
            wells_count=len(unique_wells),
            wells=unique_wells
        )
        return _create_multi_well_continuous_dataframe(
            source_df, depth_sequence, depth_curve, well_curves,
            curve_metadata, default_interpolation_method, original_depth_range, out_of_range_fill_value
        )



def _create_single_well_continuous_dataframe(
    source_df: pd.DataFrame,
    depth_sequence: np.ndarray,
    depth_curve: CurveBasicAttributes,
    well_curves: list[str],
    curve_metadata: CurveMetadata,
    default_interpolation_method: str,
    original_depth_range: tuple[float, float],
    out_of_range_fill_value: Any
) -> pd.DataFrame:
    """为单井数据创建连续型DataFrame（原有逻辑）。

    这是原有的高效转换逻辑，适用于单井数据。

    Args:
        source_df: 源离散型数据集的DataFrame
        depth_sequence: 深度序列
        depth_curve: 深度曲线属性
        well_curves: 井名曲线列表
        curve_metadata: 曲线元数据
        default_interpolation_method: 默认插值方法
        original_depth_range: 原数据集深度范围
        out_of_range_fill_value: 超出原数据集范围的填充值

    Returns:
        pd.DataFrame: 转换后的连续型DataFrame
    """
    # 创建基础DataFrame（保持原始深度列名）
    depth_column_name = depth_curve.dataframe_column_name
    new_df = pd.DataFrame({depth_column_name: depth_sequence})

    # 获取深度列名
    depth_column = depth_curve.dataframe_column_name

    # 获取井名（单井数据，取第一个井名即可）
    well_name = None
    if well_curves and well_curves[0] in source_df.columns:
        well_name_series = source_df[well_curves[0]].dropna()
        if not well_name_series.empty:
            well_name = well_name_series.iloc[0]

    # 添加井名列（保持原始井名列名）
    if well_name is not None and well_curves:
        well_column_name = well_curves[0]  # 使用原始井名列名
        new_df[well_column_name] = well_name

    # 获取需要转换的数据曲线（排除深度和井名曲线）
    exclude_columns = {depth_column}
    if well_curves:
        exclude_columns.update(well_curves)

    data_columns = [col for col in source_df.columns if col not in exclude_columns]

    # 执行插值转换
    interpolated_df = _interpolate_curves(
        source_df, depth_sequence, depth_column, data_columns,
        curve_metadata, default_interpolation_method
    )

    # 合并插值结果（使用values避免索引对齐问题）
    for column in data_columns:
        if column in interpolated_df.columns:
            new_df[column] = interpolated_df[column].values  # 使用.values避免索引对齐
            logger.debug(
                "数据列合并完成",
                operation="create_single_well_continuous_dataframe",
                column_name=column,
                has_nan_values=new_df[column].isna().any(),
                sample_values=new_df[column].head().tolist()
            )

    # 处理超出范围的数据点
    _fill_out_of_range_values(new_df, depth_sequence, original_depth_range,
                             data_columns, out_of_range_fill_value)

    logger.debug(
        "单井连续型DataFrame创建完成",
        operation="create_single_well_continuous_dataframe",
        depth_points=len(depth_sequence),
        data_columns=len(data_columns),
        well_name=well_name,
        columns=list(new_df.columns)
    )

    return new_df


def _create_multi_well_continuous_dataframe(
    source_df: pd.DataFrame,
    depth_sequence: np.ndarray,
    depth_curve: CurveBasicAttributes,
    well_curves: list[str],
    curve_metadata: CurveMetadata,
    default_interpolation_method: str,
    original_depth_range: tuple[float, float],
    out_of_range_fill_value: Any
) -> pd.DataFrame:
    """为多井数据创建连续型DataFrame（新增逻辑）。

    🚨 **关键修复**：正确处理多井数据的转换。

    修复策略：
    1. 按井名分组，对每口井单独转换
    2. 为每口井生成完整的深度序列
    3. 合并所有井的转换结果
    4. 保留部分曲线为空的数据（NaN值）

    Args:
        source_df: 源离散型数据集的DataFrame
        depth_sequence: 深度序列
        depth_curve: 深度曲线属性
        well_curves: 井名曲线列表
        curve_metadata: 曲线元数据
        default_interpolation_method: 默认插值方法
        original_depth_range: 原数据集深度范围
        out_of_range_fill_value: 超出原数据集范围的填充值

    Returns:
        pd.DataFrame: 转换后的连续型DataFrame
    """
    if not well_curves or well_curves[0] not in source_df.columns:
        raise ValueError("多井数据转换需要井名曲线")

    well_column = well_curves[0]
    unique_wells = source_df[well_column].dropna().unique()
    depth_column = depth_curve.dataframe_column_name

    # 获取需要转换的数据曲线（排除深度和井名曲线）
    exclude_columns = {depth_column}
    if well_curves:
        exclude_columns.update(well_curves)
    data_columns = [col for col in source_df.columns if col not in exclude_columns]

    # 存储每口井的转换结果
    well_dataframes = []

    for well_name in unique_wells:
        logger.debug(
            "开始处理单井转换",
            operation="create_multi_well_continuous_dataframe",
            well_name=well_name,
            data_columns=data_columns
        )

        # 提取当前井的数据
        well_data = source_df[source_df[well_column] == well_name].copy()

        if well_data.empty:
            logger.warning(
                "井数据为空，跳过",
                operation="create_multi_well_continuous_dataframe",
                well_name=well_name
            )
            continue

        # 为当前井创建连续型DataFrame
        well_continuous_df = pd.DataFrame({depth_column: depth_sequence})
        well_continuous_df[well_column] = well_name

        # 对当前井的每个数据曲线进行插值
        for column in data_columns:
            if column not in well_data.columns:
                # 如果当前井没有这个曲线，填充NaN
                well_continuous_df[column] = np.nan
                continue

            # 获取当前井当前曲线的有效数据
            valid_mask = well_data[column].notna() & well_data[depth_column].notna()
            valid_data = well_data[valid_mask]

            if valid_data.empty:
                # 当前井的当前曲线没有有效数据，填充NaN
                well_continuous_df[column] = np.nan
                logger.debug(
                    "井曲线无有效数据，填充NaN",
                    operation="create_multi_well_continuous_dataframe",
                    well_name=well_name,
                    column=column
                )
                continue

            # 执行插值
            try:
                # 使用单井插值逻辑
                single_well_interpolated = _interpolate_curves(
                    valid_data, depth_sequence, depth_column, [column],
                    curve_metadata, default_interpolation_method
                )

                if column in single_well_interpolated.columns:
                    well_continuous_df[column] = single_well_interpolated[column].values
                else:
                    well_continuous_df[column] = np.nan

                logger.debug(
                    "井曲线插值完成",
                    operation="create_multi_well_continuous_dataframe",
                    well_name=well_name,
                    column=column,
                    valid_points=len(valid_data),
                    has_nan_values=well_continuous_df[column].isna().any()
                )

            except Exception as e:
                logger.warning(
                    "井曲线插值失败，填充NaN",
                    operation="create_multi_well_continuous_dataframe",
                    well_name=well_name,
                    column=column,
                    error=str(e)
                )
                well_continuous_df[column] = np.nan

        # 处理超出范围的数据点
        well_depth_range = (
            well_data[depth_column].min(),
            well_data[depth_column].max()
        )
        _fill_out_of_range_values(
            well_continuous_df, depth_sequence, well_depth_range,
            data_columns, out_of_range_fill_value
        )

        well_dataframes.append(well_continuous_df)

        logger.debug(
            "单井转换完成",
            operation="create_multi_well_continuous_dataframe",
            well_name=well_name,
            converted_rows=len(well_continuous_df)
        )

    # 合并所有井的转换结果
    if not well_dataframes:
        # 没有有效的井数据，返回空DataFrame
        logger.warning(
            "没有有效的井数据",
            operation="create_multi_well_continuous_dataframe"
        )
        return pd.DataFrame()

    # 使用concat合并所有井的数据
    result_df = pd.concat(well_dataframes, ignore_index=True)

    logger.debug(
        "多井连续型DataFrame创建完成",
        operation="create_multi_well_continuous_dataframe",
        wells_count=len(unique_wells),
        total_rows=len(result_df),
        columns=list(result_df.columns)
    )

    return result_df


def _interpolate_curves(
    source_df: pd.DataFrame,
    depth_sequence: np.ndarray,
    depth_column: str,
    data_columns: list[str],
    curve_metadata: CurveMetadata,
    default_interpolation_method: str
) -> pd.DataFrame:
    """对曲线数据进行插值处理。

    Args:
        source_df: 源DataFrame
        depth_sequence: 目标深度序列
        depth_column: 深度列名
        data_columns: 数据列名列表
        curve_metadata: 曲线元数据
        default_interpolation_method: 默认插值方法

    Returns:
        pd.DataFrame: 插值后的DataFrame（使用默认整数索引）

    Note:
        遵循logwp数据模型要求：DataFrame使用默认整数索引，深度信息作为普通列存储
    """
    # 创建结果DataFrame（使用默认整数索引，符合logwp数据模型要求）
    result_df = pd.DataFrame()

    # 获取源数据并排序
    temp_df = source_df.copy()
    temp_df = temp_df.sort_values(by=depth_column).reset_index(drop=True)

    # 对每个数据曲线进行插值
    for column in data_columns:
        curve_attrs = curve_metadata.get_curve(column)
        if curve_attrs is not None:
            # 使用CurveBasicAttributes的智能插值方法选择
            interpolation_method = curve_attrs.get_recommended_interpolation_method(
                default_interpolation_method
            )
        else:
            # 如果找不到曲线属性，使用默认方法
            interpolation_method = default_interpolation_method

        try:
            # 检查数据类型
            column_data = temp_df[column].dropna()
            if column_data.empty:
                # 如果列为空，填充NaN
                interpolated_values = np.full(len(depth_sequence), np.nan)
            elif column_data.dtype == 'object' or interpolation_method == "nearest":
                # 字符串类型或最近邻插值
                from scipy.interpolate import interp1d

                # 为字符串数据创建数值映射
                if column_data.dtype == 'object':
                    unique_values = column_data.unique()
                    value_map = {val: i for i, val in enumerate(unique_values)}
                    reverse_map = {i: val for val, i in value_map.items()}

                    # 转换为数值
                    numeric_values = temp_df[column].map(value_map).values

                    # 最近邻插值
                    f = interp1d(
                        temp_df[depth_column].values,
                        numeric_values,
                        kind='nearest',
                        bounds_error=False,
                        fill_value='extrapolate'
                    )
                    numeric_result = f(depth_sequence)

                    # 转换回字符串
                    interpolated_values = np.array([
                        reverse_map.get(int(val), np.nan) if not np.isnan(val) else np.nan
                        for val in numeric_result
                    ])
                else:
                    # 数值型最近邻插值
                    f = interp1d(
                        temp_df[depth_column].values,
                        temp_df[column].values,
                        kind='nearest',
                        bounds_error=False,
                        fill_value='extrapolate'
                    )
                    interpolated_values = f(depth_sequence)
            elif interpolation_method == "linear":
                # 线性插值（仅适用于数值型）
                interpolated_values = np.interp(
                    depth_sequence,
                    temp_df[depth_column].values,
                    temp_df[column].values
                )
            else:
                # 其他方法使用scipy.interpolate
                from scipy.interpolate import interp1d
                try:
                    f = interp1d(
                        temp_df[depth_column].values,
                        temp_df[column].values,
                        kind=interpolation_method,
                        bounds_error=False,
                        fill_value='extrapolate'
                    )
                    interpolated_values = f(depth_sequence)
                except ValueError:
                    # 如果插值方法不支持，回退到线性插值
                    logger.warning(
                        f"插值方法 {interpolation_method} 不支持，回退到线性插值",
                        operation="interpolate_curves",
                        curve_name=column
                    )
                    interpolated_values = np.interp(
                        depth_sequence,
                        temp_df[depth_column].values,
                        temp_df[column].values
                    )

            # 将插值结果添加到DataFrame（使用默认整数索引）
            result_df[column] = interpolated_values

            # 检查NaN值（处理字符串类型）
            try:
                has_nan_values = np.isnan(interpolated_values).any() if interpolated_values.dtype.kind in 'fc' else False
            except (TypeError, ValueError):
                has_nan_values = pd.isna(interpolated_values).any()

            logger.debug(
                "曲线插值完成",
                operation="interpolate_curves",
                curve_name=column,
                interpolation_method=interpolation_method,
                original_points=len(temp_df),
                interpolated_points=len(interpolated_values),
                has_nan_values=has_nan_values,
                sample_values=interpolated_values[:5].tolist() if len(interpolated_values) > 0 else []
            )

        except Exception as e:
            # 插值失败时的备选方案
            logger.warning(
                "曲线插值失败，使用备选方案",
                operation="interpolate_curves",
                curve_name=column,
                failed_method=interpolation_method,
                error=str(e)
            )

            # 检查数据类型，选择合适的备选方案
            column_data = temp_df[column].dropna()
            if column_data.dtype == 'object':
                # 字符串类型使用最近邻插值
                try:
                    from scipy.interpolate import interp1d
                    unique_values = column_data.unique()
                    value_map = {val: i for i, val in enumerate(unique_values)}
                    reverse_map = {i: val for val, i in value_map.items()}

                    numeric_values = temp_df[column].map(value_map).values
                    f = interp1d(
                        temp_df[depth_column].values,
                        numeric_values,
                        kind='nearest',
                        bounds_error=False,
                        fill_value='extrapolate'
                    )
                    numeric_result = f(depth_sequence)
                    interpolated_values = np.array([
                        reverse_map.get(int(val), np.nan) if not np.isnan(val) else np.nan
                        for val in numeric_result
                    ])
                except Exception:
                    # 最后的备选：填充最常见的值
                    most_common = column_data.mode()
                    fill_value = most_common.iloc[0] if len(most_common) > 0 else np.nan
                    interpolated_values = np.full(len(depth_sequence), fill_value)
            else:
                # 数值类型使用线性插值
                try:
                    interpolated_values = np.interp(
                        depth_sequence,
                        temp_df[depth_column].values,
                        temp_df[column].values
                    )
                except Exception:
                    # 最后的备选：填充NaN
                    interpolated_values = np.full(len(depth_sequence), np.nan)

            # 将备选插值结果添加到DataFrame（使用默认整数索引）
            result_df[column] = interpolated_values

    return result_df


def _fill_out_of_range_values(
    df: pd.DataFrame,
    depth_sequence: np.ndarray,
    original_depth_range: tuple[float, float],
    data_columns: list[str],
    out_of_range_fill_value: Any
) -> None:
    """填充超出原数据集范围的数据点。

    Args:
        df: 目标DataFrame（就地修改）
        depth_sequence: 深度序列
        original_depth_range: 原数据集深度范围
        data_columns: 数据列名列表
        out_of_range_fill_value: 超出范围的填充值
    """
    # 确定超出范围的索引
    out_of_range_mask = (
        (depth_sequence < original_depth_range[0]) |
        (depth_sequence > original_depth_range[1])
    )

    # 如果有超出范围的点，进行填充
    if out_of_range_mask.any():
        for column in data_columns:
            if column in df.columns:
                df.loc[out_of_range_mask, column] = out_of_range_fill_value

        out_of_range_count = out_of_range_mask.sum()
        logger.debug(
            "超出范围数据点填充完成",
            operation="fill_out_of_range_values",
            out_of_range_count=out_of_range_count,
            total_points=len(depth_sequence),
            fill_value=str(out_of_range_fill_value)
        )


def _create_continuous_metadata(
    source_metadata: CurveMetadata,
    depth_curve: CurveBasicAttributes,
    sampling_interval: float
) -> CurveMetadata:
    """创建连续型数据集的曲线元数据。

    Args:
        source_metadata: 源曲线元数据
        depth_curve: 原深度曲线属性
        sampling_interval: 采样间隔

    Returns:
        CurveMetadata: 新的曲线元数据
    """
    # 深拷贝源元数据
    new_metadata = copy.deepcopy(source_metadata)

    # 移除原有的深度曲线
    if new_metadata.has_curve(depth_curve.name):
        new_metadata.remove_curve(depth_curve.name)

    # 创建新的深度曲线（保持原始深度曲线名称）
    new_depth_curve = CurveBasicAttributes.create_1d_curve(
        name=depth_curve.name,  # 保持原始名称
        unit=depth_curve.unit,  # 使用原深度曲线的单位
        category=WpCurveCategory.IDENTIFIER,
        depth_role=WpDepthRole.SINGLE,
        description=f"连续深度曲线，采样间隔{sampling_interval}{depth_curve.unit}"
    )
    # 设置DataFrame列名（保持原始DataFrame列名）
    object.__setattr__(new_depth_curve, 'dataframe_column_name', depth_curve.dataframe_column_name)

    # 添加新的深度曲线
    new_metadata.add_curve(new_depth_curve)

    # 井名曲线保持原始名称，无需修改dataframe_column_name

    # 更新时间戳
    new_metadata.modified_at = datetime.now()

    logger.debug(
        "连续型曲线元数据创建完成",
        operation="create_continuous_metadata",
        total_curves=new_metadata.get_curve_count(),
        depth_curve=depth_curve.name,
        sampling_interval=sampling_interval,
        well_curves=len(new_metadata.get_well_identifier_curves())
    )

    return new_metadata


def _create_continuous_dataframe_with_fixed_range(
    source_df: pd.DataFrame,
    depth_sequence: np.ndarray,
    depth_curve: CurveBasicAttributes,
    well_curves: list[str],
    curve_metadata: CurveMetadata,
    default_interpolation_method: str,
    original_depth_range: tuple[float, float],
    out_of_range_fill_value: Any
) -> pd.DataFrame:
    """使用固定深度范围创建连续型DataFrame（用户指定深度范围时）。"""
    return _create_continuous_dataframe(
        source_df, depth_sequence, depth_curve, well_curves,
        curve_metadata, default_interpolation_method, original_depth_range, out_of_range_fill_value
    )


def _create_continuous_dataframe_per_well(
    source_df: pd.DataFrame,
    depth_curve: CurveBasicAttributes,
    well_curves: list[str],
    curve_metadata: CurveMetadata,
    sampling_interval: float,
    interpolation_method: str,
    out_of_range_fill_value: Any
) -> tuple[pd.DataFrame, tuple[float, float]]:
    """按井分别创建连续型DataFrame，避免跨井的无意义深度范围。

    Returns:
        tuple[pd.DataFrame, tuple[float, float]]: (转换后的DataFrame, 实际深度范围)
    """
    # 检测井的数量
    unique_wells = []
    if well_curves and well_curves[0] in source_df.columns:
        unique_wells = source_df[well_curves[0]].dropna().unique().tolist()

    if len(unique_wells) <= 1:
        # 单井数据：使用井自己的深度范围
        well_data = source_df
        if unique_wells:
            well_data = source_df[source_df[well_curves[0]] == unique_wells[0]]

        depth_column = depth_curve.dataframe_column_name
        well_depths = well_data[depth_column].dropna()
        if well_depths.empty:
            # 没有有效深度数据
            return pd.DataFrame(), (0.0, 0.0)

        well_depth_range = (float(well_depths.min()), float(well_depths.max()))
        depth_sequence = _generate_depth_sequence(well_depth_range, sampling_interval)

        new_df = _create_continuous_dataframe(
            source_df, depth_sequence, depth_curve, well_curves,
            curve_metadata, interpolation_method, well_depth_range, out_of_range_fill_value
        )

        actual_depth_range = (depth_sequence[0], depth_sequence[-1])
        return new_df, actual_depth_range

    else:
        # 多井数据：按井分别处理，然后合并
        well_dataframes = []
        all_depth_ranges = []

        depth_column = depth_curve.dataframe_column_name

        for well in unique_wells:
            well_data = source_df[source_df[well_curves[0]] == well]
            well_depths = well_data[depth_column].dropna()

            if well_depths.empty:
                continue

            well_depth_range = (float(well_depths.min()), float(well_depths.max()))
            well_depth_sequence = _generate_depth_sequence(well_depth_range, sampling_interval)

            well_df = _create_continuous_dataframe(
                well_data, well_depth_sequence, depth_curve, well_curves,
                curve_metadata, interpolation_method, well_depth_range, out_of_range_fill_value
            )

            if not well_df.empty:
                well_dataframes.append(well_df)
                all_depth_ranges.extend([well_depth_sequence[0], well_depth_sequence[-1]])

        if not well_dataframes:
            return pd.DataFrame(), (0.0, 0.0)

        # 合并所有井的数据
        combined_df = pd.concat(well_dataframes, ignore_index=True)

        # 计算实际深度范围
        if all_depth_ranges:
            actual_depth_range = (min(all_depth_ranges), max(all_depth_ranges))
        else:
            actual_depth_range = (0.0, 0.0)

        return combined_df, actual_depth_range
