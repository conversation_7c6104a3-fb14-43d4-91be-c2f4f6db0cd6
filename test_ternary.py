"""
测试修复后的三元图脚本
"""

import sys
import os
sys.path.append('scripts/snippets')

from plotly_ternary import generate_sample_data, create_ternary_plot

def test_basic_ternary():
    """测试基础三元图功能"""
    
    # 生成测试数据
    data = generate_sample_data(n_points=50)
    
    print("数据统计:")
    print(f"Macro: {data['Macro'].min():.1f} - {data['Macro'].max():.1f}")
    print(f"Meso: {data['Meso'].min():.1f} - {data['Meso'].max():.1f}")
    print(f"Micro: {data['Micro'].min():.1f} - {data['Micro'].max():.1f}")
    print(f"总和检查: {(data['Macro'] + data['Meso'] + data['Micro']).min():.1f} - {(data['Macro'] + data['Meso'] + data['Micro']).max():.1f}")
    print(f"Perm: {data['Perm'].min():.2f} - {data['Perm'].max():.2f}")
    
    # 创建图表
    fig = create_ternary_plot(
        data=data,
        title="Test Ternary Plot",
        well_name="Test Well",
        colorscale='Viridis'
    )
    
    # 显示图表
    fig.show()
    
    print("测试完成！")

if __name__ == "__main__":
    test_basic_ternary()
