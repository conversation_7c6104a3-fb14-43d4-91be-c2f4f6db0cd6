# `scape.core.obmiq` (PyTorch版) 组件开发计划与设计说明书

**版本: 1.0**

## 1. 目标与定位

### 1.1. 项目目标

开发一个位于 `scape.core.obmiq` 的新组件，作为**可追踪机器学习组件开发框架**的一个标准实现。该组件的核心任务是：基于测井数据，训练一个基于 **PyTorch** 的混合输入深度学习模型，以精准预测油基泥浆侵入导致的 `DT2_P50` 和 `DPHIT_NMR` 变化。

**本开发计划的所有技术实现细节，均严格遵循《SCAPE_方法说明书_V5_OBMIQ建模_Pytorch版.md》中定义的算法、架构与规程。**

### 1.2. 架构定位

*   **框架遵从性**: `obmiq` 将严格遵循《可追踪机器学习组件开发框架》，实现为一个包含`训练`和`预测`两个步骤（Steps）的**多步骤包**。
*   **技术栈**: 采用 `PyTorch` 构建混合输入模型，`Optuna` 进行超参数寻优，并深度集成 `logwp.extras.tracking` 和 `logwp.extras.plotting`。
*   **可复现性**: 整个流程，从超参数搜索到最终模型训练，都将被`RunContext`追踪，确保结果的完全可复现。

---

## 2. 核心设计决策与“干跑”分析

本节是开发前的核心“干跑”分析，旨在预演关键技术点，统一设计决策。

### 2.1. 遵循多步骤包架构

我们将采用标准的多步骤包结构，将训练和预测逻辑分离到不同的门面（Facade）中，同时共享内部逻辑和配置。

*   `training_facade.py`: 实现 `run_obmiq_training_step()`，负责超参数寻优和最终模型训练。
*   `prediction_facade.py`: 实现 `run_obmiq_prediction_step()`，负责使用已训练的模型进行预测。
*   `config.py`: 定义 `ObmiqTrainingConfig` 和 `ObmiqPredictionConfig`。
*   `constants.py`: 定义所有产物和绘图模板的常量。
*   `artifact_handler.py`: 定义一个共享的 `ObmiqArtifactHandler`。
*   `internal/`: 包含共享的内部实现，如模型构建函数 `model_builder.py`。

### 2.2. Facade接口设计 (参数传递)

Facade 接口设计将保持与 TensorFlow 版本一致，以遵循框架的“关注点分离”原则。

#### **训练步骤 Facade 签名**

```python
# training_facade.py
def run_obmiq_training_step(
    config: ObmiqTrainingConfig,
    ctx: RunContext,
    train_bundle: WpDataFrameBundle,
    *,
    sequence_feature: str,
    normalization_feature: str,
    tabular_features: list[str],
    target_features: list[str],
    group_column: str,
    t2_time_axis: np.ndarray
) -> dict[str, Any]:
    # ...
```

#### **预测步骤 Facade 签名**

```python
# prediction_facade.py
def run_obmiq_prediction_step(
    config: ObmiqPredictionConfig,
    ctx: RunContext,
    model_assets: dict[str, Any],
    prediction_bundle: WpDataFrameBundle,
    *,
    source_t2_time_axis: np.ndarray,
    output_curve_names: tuple[str, str]
) -> dict[str, Any]:
    # ...
```

### 2.3. 核心产物设计 (模型资产包)

训练步骤的核心产出是一个**“模型资产包” (Model Assets)**。与 TensorFlow 版本不同，PyTorch 版本的资产包将包含 PyTorch 特有的产物。

**模型资产包结构 (将被 `ObmiqArtifactHandler` 序列化/反序列化):**

```python
# 一个Python字典，可以被pickle或joblib保存
model_assets = {
    "model_state_dict": ...,  # PyTorch模型的权重 (model.state_dict())
    "model_hyperparameters": ..., # 一个包含模型架构超参数的字典
    "preprocessors": {
        "tabular_scaler": ..., # fit好的StandardScaler对象
        # ... 其他预处理器
    },
    "metadata": {
        "sequence_feature": "PHI_T2_DIST_CUM",
        "normalization_feature": "PHIT_NMR",
        "tabular_features_ordered": ['GR', 'DEN', ...], # 必须保留顺序
        "target_features_ordered": ['DT2_P50', 'DPHIT_NMR'],
        "standard_t2_time_axis": np.array([...]) # 标准T2时间轴
    }
}
```
这种设计将模型权重、架构配置、预处理器和元数据紧密绑定，确保了预测时的一致性。

### 2.4. T2谱重采样策略

此策略与 TensorFlow 版本完全相同，由 `prediction_facade` 在预测时调用 `logwp.extras.nmr.resampling.resample_t2_spectrum` 函数实现。

### 2.5. 训练工作流内部规程 (Internal Procedures)

`training_facade`的逻辑将被重构以适配 PyTorch 和 Optuna。

1.  **数据处理规程 (`internal/data_handler.py`)**:
    *   实现 `OBMIQDataset` PyTorch数据集类和 `create_dataloaders` 辅助函数。
    *   **关键职责**: 必须支持在交叉验证的每一折（fold）内部，对训练数据进行独立的预处理器（如`StandardScaler`）拟合，并用拟合好的处理器转换该折的训练集和验证集。**这是防止数据泄露的核心**。

1.  **超参数寻优规程 (`internal/tuning_procedure.py`)**:
    *   实现`run_hyperparameter_tuning_cv()`函数。
    *   负责执行完整的`LOWO-CV` + `Optuna`嵌套循环。
    *   其核心是一个 `Objective` 类（或函数），该类内部包含**手动的 PyTorch 训练/验证循环**。
    *   输出为在所有CV折中找到的**最佳超参数组合** (一个字典) 和详细的CV性能报告。
    *   **关键职责**: 在CV循环的每一折中，调用 `data_handler` 生成带有正确预处理器状态的 `DataLoader`。

2.  **最终模型训练规程 (`internal/final_training_procedure.py`)**:
    *   实现`train_final_model()`函数。
    *   接收最佳超参数，在**全部建模数据**上训练最终模型。
    *   利用手动实现的早停机制防止过拟合。
    *   输出包含模型、预处理器和元数据的**“模型资产包”**。

---

## 3. 拟定目录与文件结构

目录结构将与 TensorFlow 版本保持高度一致，以遵循框架约定。

```
scape/core/obmiq/
├── __init__.py               # 导出所有步骤的公共API
├── README.md                 # (待编写) 组件使用说明文档
├── training_facade.py        # 训练步骤的门面
├── prediction_facade.py      # 预测步骤的门面
├── config.py                 # Pydantic配置模型 (ObmiqTrainingConfig, ObmiqPredictionConfig)
├── constants.py              # 产物和绘图配置常量
├── artifact_handler.py       # ObmiqArtifactHandler
├── plotting.py               # 从数据快照复现图表的功能
├── plot_profiles.py          # 注册本模块专属的PlotProfile
└── internal/                 # 内部实现细节
    ├── __init__.py
    ├── model_builder.py      # PyTorch模型构建 (OBMIQPyTorchModel, AdaptiveLossModule)
    ├── data_handler.py       # 数据预处理与PyTorch Dataset/DataLoader创建
    ├── tuning_procedure.py   # 超参数寻优规程 (Optuna)
    └── final_training_procedure.py # 最终模型训练规程
```

---

## 4. API 与核心组件定义 (代码骨架)

### `config.py`

PyTorch 版本的配置将更侧重于控制训练流程，而非定义搜索空间。

```python
from __future__ import annotations

from pydantic import BaseModel, Field, PositiveInt, conint, confloat

class ObmiqTrainingConfig(BaseModel):
    """
    OBMIQ (PyTorch版) 训练步骤的流程控制配置。

    该配置模型不定义超参数的搜索空间（由Optuna在代码中动态定义），
    而是控制整个训练和寻优流程的行为。
    """
    # Reproducibility
    random_seed: int = Field(2025, description="用于所有随机操作的全局种子，确保可复现性")

    # Optuna Hyperparameter Tuning
    n_trials: PositiveInt = Field(
        100,
        description="Optuna超参数搜索的总试验次数"
    )

    # Training Loop Control
    max_epochs_per_trial: PositiveInt = Field(
        50,
        description="在超参数寻优的单次试验中，模型训练的最大轮次"
    )
    final_train_epochs: PositiveInt = Field(
        150,
        description="在找到最佳超参数后，最终模型训练的最大轮次"
    )
    patience: PositiveInt = Field(
        10,
        description="早停机制的耐心轮次数。如果在patience个轮次内验证集性能没有提升，则停止训练。"
    )
    batch_size: conint(gt=0) = Field(
        64,
        description="训练和验证时使用的批处理大小"
    )

    # Cross-Validation Data Split
    val_split_ratio: confloat(gt=0, lt=1) = Field(
        0.2,
        description="在每个LOWO-CV折内部，用于从训练集中划分出验证集的比例"
    )

class ObmiqPredictionConfig(BaseModel):
    """
    OBMIQ (PyTorch版) 预测步骤的配置。

    目前为空，为未来可能增加的预测时参数（如推理批处理大小）保留扩展性。
    """
    pass
```

### `constants.py`

产物名称将与 TensorFlow 版本基本保持一致，仅模型产物名称需调整。

```python
from enum import Enum


# --- Artifact Naming Conventions ---
# Follows the standard: <step_name>.<category>.<specific_name>
# step_name for this component is 'obmiq_training' or 'obmiq_prediction'

class ObmiqTrainingArtifacts(str, Enum):
    """
    Defines the logical names for all artifacts produced by the OBMIQ training step.
    """
    # --- Core Model Asset ---
    MODEL_ASSETS = "obmiq_training.models.assets_pytorch" # 核心产物

    # --- Reports ---
    TUNING_REPORT = "obmiq_training.reports.hyperparameter_tuning"
    CV_PERFORMANCE_REPORT = "obmiq_training.reports.cv_performance"

    # --- Training History ---
    FINAL_TRAINING_HISTORY_PLOT = "obmiq_training.plots.final_training_history"
    FINAL_TRAINING_HISTORY_DATA = "obmiq_training.data_snapshots.final_training_history"

    # --- Final Model Evaluation on Training Data ---
    FINAL_MODEL_EVALUATION_DATA = "obmiq_training.data_snapshots.final_model_evaluation"
    EVAL_CROSSPLOT_DT2_P50 = "obmiq_training.plots.eval_crossplot_dt2_p50"
    EVAL_CROSSPLOT_DPHIT_NMR = "obmiq_training.plots.eval_crossplot_dphit_nmr"
    EVAL_RESIDUALS_PLOT_DT2_P50 = "obmiq_training.plots.eval_residuals_plot_dt2_p50"
    EVAL_RESIDUALS_PLOT_DPHIT_NMR = "obmiq_training.plots.eval_residuals_plot_dphit_nmr"
    EVAL_RESIDUALS_HIST_DT2_P50 = "obmiq_training.plots.eval_residuals_hist_dt2_p50"
    EVAL_RESIDUALS_HIST_DPHIT_NMR = "obmiq_training.plots.eval_residuals_hist_dphit_nmr"

    # --- Model Interpretability ---
    SHAP_SUMMARY_PLOT = "obmiq_training.plots.shap_summary_plot"
    GRAD_CAM_EXAMPLES = "obmiq_training.plots.grad_cam_examples" # This might be a directory

class ObmiqPredictionArtifacts(str, Enum):
    """
    Defines the logical names for all artifacts produced by the OBMIQ prediction step.
    """
    # Core prediction output, also serves as the data snapshot for all plots
    PREDICTIONS = "obmiq_prediction.datasets.predictions"

class ObmiqPlotProfiles(str, Enum):
    """
    Defines the names for PlotProfile templates specific to the OBMIQ component.
    These names are used for registration and retrieval from the central plot registry.
    """
    # Module-level base profile
    BASE = "obmiq.base"

    # Specific plot profiles
    TRAINING_HISTORY = "obmiq.training_history"
    CROSSPLOT = "obmiq.crossplot"
    RESIDUALS_PLOT = "obmiq.residuals_plot"
    RESIDUALS_HIST = "obmiq.residuals_hist"
    SHAP_SUMMARY = "obmiq.shap_summary"
    GRAD_CAM = "obmiq.grad_cam"
```

### `internal/model_builder.py`

此文件将包含 PyTorch 模型的定义，严格遵循方法说明书。

```python
import torch
import torch.nn as nn

class AdaptiveLossModule(nn.Module):
    # ... 实现方法说明书 2.8 节中的自适应损失模块 ...
    pass

class OBMIQPyTorchModel(nn.Module):
    # ... 实现方法说明书 2.8 节中的混合输入模型架构 ...
    pass
```

### `training_facade.py`

`facade` 将负责编排 PyTorch 的训练流程。

```python
from .internal import tuning_procedure, final_training_procedure
from .config import ObmiqTrainingConfig
from .constants import ObmiqTrainingArtifacts
from .artifact_handler import ObmiqArtifactHandler
from logwp.extras.tracking import RunContext
from logwp.models.datasets.bundle import WpDataFrameBundle # Assuming this type exists

def run_obmiq_training_step(
    config: ObmiqTrainingConfig,
    ctx: RunContext,
    train_bundle: WpDataFrameBundle,
    # ... other data selector args like sequence_feature, etc.
):
    """执行OBMIQ模型训练、超参数寻优和最终模型交付 (PyTorch版)。"""
    logger = get_logger()
    step_dir = ctx.get_step_dir("obmiq_training_pytorch")

    # 1. 内部规程一：超参数寻优
    # Facade层不直接处理数据，而是将原始数据(train_bundle)和配置传递给内部规程。
    # tuning_procedure 将负责编排LOWO-CV循环，并在每一折内部调用 data_handler
    # 来正确地拟合预处理器和创建DataLoader，以避免数据泄露。
    logger.info("开始交叉验证与超参数寻优 (Optuna)...")
    best_hps, cv_report_df = tuning_procedure.run_hyperparameter_tuning_cv(
        train_bundle=train_bundle, config=config, # ... pass other args
    )
    logger.info(f"最佳超参数已找到: {best_hps}")
    # ... (此处应保存 cv_report_df 并注册为产物) ...

    # 2. 内部规程二：最终模型训练
    # final_training_procedure 将负责在全量数据上重新拟合预处理器，然后训练模型。
    logger.info("开始在全部数据上训练最终模型...")
    model_assets = final_training_procedure.train_final_model(
        train_bundle=train_bundle, best_hps=best_hps, config=config, # ... pass other args
    )

    # 3. 保存核心产物：模型资产包
    handler = ObmiqArtifactHandler()
    assets_path = step_dir / "model_assets_pytorch.pkl"
    handler.save_model_assets(model_assets, assets_path)
    ctx.register_artifact(
        assets_path.relative_to(ctx.run_dir),
        ObmiqTrainingArtifacts.MODEL_ASSETS.value
    )

    # ... 保存和注册其他报告和图表产物 ...

    return {"status": "completed"}
```

### `prediction_facade.py`

预测 `facade` 将加载 PyTorch 模型并执行推断。

```python
import torch
from .internal.model_builder import OBMIQPyTorchModel

def run_obmiq_prediction_step(...):
    """使用训练好的OBMIQ PyTorch模型进行预测。"""
    # 1. 加载模型资产
    model_state_dict = model_assets["model_state_dict"]
    model_hyperparameters = model_assets["model_hyperparameters"]
    preprocessors = model_assets["preprocessors"]
    metadata = model_assets["metadata"]

    # 2. 重建模型
    model = OBMIQPyTorchModel(model_hyperparameters, ...)
    model.load_state_dict(model_state_dict)
    model.eval() # 设置为评估模式

    # 3. 数据预处理 (重采样、归一化、标准化)
    # ...

    # 4. 执行预测
    with torch.no_grad(): # 禁用梯度计算
        inputs_tensor = ... # 将预处理后的数据转换为PyTorch张量
        predictions = model(inputs_tensor)

    # 5. 后处理与产物生成
    # ...

    return {"status": "completed"}
```

---

## 5. 开发步骤与任务分解 (小步快跑策略)

我们将开发过程分解为以下可独立验证的微任务。

### **协作模式说明**

本开发计划将采用人机协作的模式完成：
*   **您 (开发者)**: 负责执行环境准备、文件创建/复制、代码应用等操作。
*   **我 (Gemini Code Assist)**: 负责提供所有核心模块的编码实现和重构方案。

下文中的每一项任务都将明确我们各自的职责。

### **代码复用建议**

为了加速开发，建议您先将以下文件从 `scape/core/obmiq_tf` 复制到 `scape/core/obmiq`，然后在此基础上进行修改：
*   `config.py`
    *   **注意**: 此文件需要**重写**。其角色从定义KerasTuner的“搜索空间”转变为定义Optuna的“训练流程控制参数”（如`n_trials`, `max_epochs`）。
*   `constants.py`
    *   可基本复用，只需将模型产物名称从 `.keras` 调整为 PyTorch 相关的名称。
*   `artifact_handler.py`
    *   可复用大部分逻辑，但 `save/load_model_assets` 方法需要重写，以处理 PyTorch 的 `state_dict` 和超参数字典。
*   `plotting.py`
    *   完全可复用。
*   `plot_profiles.py`
    *   完全可复用。

---

### **里程碑 1: 基础架构与模型构建 (1-2天)**

*   **任务 1.1: 项目骨架搭建与文件复制**
    *   **协作模式**: 您负责创建目录和复制文件，为后续编码工作准备好基础环境。
    *   **操作 (由您完成)**: 创建 `scape/core/obmiq` 目录及所有必需的空文件。按照开发计划中的“代码复用建议”复制可复用的文件。
    *   **验证**: 确认目录结构符合规范。

*   **任务 1.2: 修改配置与常量**
    *   **协作模式**: 我将提供 `config.py` 和 `constants.py` 的完整代码，您负责将其应用到文件中。
    *   **操作 (由我完成)**: 提供 `config.py` 和 `constants.py` 的具体实现代码。
    *   **验证**: 单元测试可以成功实例化新的 `ObmiqTrainingConfig`。

*   **任务 1.3: 实现 PyTorch 模型 (`internal/model_builder.py`)**
    *   **协作模式**: 我将提供 `OBMIQPyTorchModel` 和 `AdaptiveLossModule` 的完整实现，您负责应用。
    *   **操作 (由我完成)**: 提供 `internal/model_builder.py` 的具体实现代码。
    *   **验证**: 编写单元测试，传入一个模拟的超参数字典，断言构建出的模型结构正确（层、输入输出形状等）。

*   **任务 1.4: 实现数据处理模块 (`internal/data_handler.py`)**
    *   **协作模式**: 我将提供 `OBMIQDataset` 和 `DataLoader` 创建逻辑的完整实现，您负责应用。
    *   **操作 (由我完成)**: 提供 `internal/data_handler.py` 的具体实现代码。
    *   **验证**: 单元测试验证数据可以被正确处理并加载。

---

### **里程碑 2: 训练步骤核心逻辑 (3-4天)**

*   **任务 2.1: 实现超参数寻优规程 (`internal/tuning_procedure.py`)**
    *   **协作模式**: 我将提供 `run_hyperparameter_tuning_cv` 函数的完整实现，您负责应用。
    *   **操作 (由我完成)**: 实现 `run_hyperparameter_tuning_cv` 函数。核心是实现一个 `Objective` 类，其中包含完整的手动训练/验证循环和早停逻辑。
    *   **验证**: 编写集成测试，使用少量数据和简化的搜索空间（`n_trials=2`），验证 `Optuna` 循环能跑通并返回一个最佳超参数字典。

*   **任务 2.2: 实现最终模型训练规程 (`internal/final_training_procedure.py`)**
    *   **协作模式**: 我将提供 `train_final_model` 函数的完整实现，您负责应用。
    *   **操作 (由我完成)**: 实现 `train_final_model` 函数，接收最佳超参数，在全量数据上训练并返回模型资产包。
    *   **验证**: 单元测试验证该函数能正确构建模型、训练并返回结构正确的资产包字典。

*   **任务 2.3: 组装训练门面 (`training_facade.py`)**
    *   **协作模式**: 我将提供 `run_obmiq_training_step` 函数的完整实现，您负责应用。
    *   **操作 (由我完成)**: 编写 `run_obmiq_training_step` 函数，编排对数据预处理、超参数寻优和最终训练这三个规程的调用。
    *   **验证**: 扩充集成测试，使用 `MagicMock` 模拟 `RunContext`，验证 `facade` 的调用流程和产物注册逻辑。

---

### **里程碑 3: 预测步骤与最终集成 (2-3天)**

*   **任务 3.1: 完善产物处理器 (`artifact_handler.py`)**
    *   **协作模式**: 我将提供 `ObmiqArtifactHandler` 的重构代码，您负责应用。
    *   **操作 (由我完成)**: 确保 `ObmiqArtifactHandler` 能正确处理 PyTorch 的 `state_dict`。
    *   **验证**: 单元测试验证模型资产包可以被成功保存和加载。

*   **任务 3.2: 实现预测门面 (`prediction_facade.py`)**
    *   **协作模式**: 我将提供 `run_obmiq_prediction_step` 函数的完整实现，您负责应用。
    *   **操作 (由我完成)**: 实现 `run_obmiq_prediction_step` 函数。
    *   **验证**: 编写集成测试，手动创建一个模拟的 `model_assets` 字典，调用预测门面，并断言输出 `Bundle` 的数据形状和列名正确。

*   **任务 3.3: 端到端工作流测试**
    *   **协作模式**: 我将提供端到端集成测试的完整代码，您负责应用和运行。
    *   **操作 (由我完成)**: 编写一个最终的端到端集成测试，按顺序调用训练和预测 `facade`。
    *   **验证**: 断言预测步骤能成功运行，并验证所有产物文件都已正确生成。

---

### **里程碑 4: 文档与收尾 (1天)**

*   **任务 4.1: 编写组件 `README.md`**
    *   **协作模式**: 我将为您生成 `README.md` 的初稿，您负责审查和完善。
    *   **操作 (由我完成)**: 为 `obmiq` 组件编写一份详细的使用说明文档。
    *   **验证**: 文档清晰地说明了组件的目标、API、配置、产物和使用示例。

*   **任务 4.2: 代码审查与最终清理**
    *   **协作模式**: 我们共同对所有代码进行最终审查，确保符合编码规范，完善注释。
    *   **操作 (由您完成)**: 发起代码审查请求。
    *   **操作 (由我完成)**: 提供审查意见和最终的清理建议。
    *   **验证**: 代码整洁、可读性高，所有测试通过。

---

## 6. 风险评估与应对

1.  **PyTorch训练循环复杂性**: 手动编写训练、验证、早停循环比使用 Keras 的 `.fit()` 更容易出错。
    *   **应对**: 严格进行单元测试和集成测试，确保循环逻辑的正确性。在 `internal` 模块中将训练循环逻辑封装成可重用的函数。
2.  **性能**: PyTorch 在某些硬件上的性能可能与 TensorFlow 不同。
    *   **应对**: 在开发后期进行性能基准测试，必要时使用 `torch.compile` 或 `AMP` (自动混合精度) 进行优化。
3.  **模型可解释性**: SHAP 和 Grad-CAM 的实现需要找到 PyTorch 兼容的库。
    *   **应对**: 提前调研 `captum` 等 PyTorch 官方或社区推荐的可解释性库，并预留开发时间。
4.  **依赖管理**: 切换到 PyTorch 会改变核心依赖。
    *   **应对**: 在 `pyproject.toml` 中清晰地定义 `torch` 和 `optuna` 的依赖，并移除 `tensorflow` 和 `keras-tuner`。

---
