from __future__ import annotations

"""logwp.models.well_project - 井工程项目聚合根

WpWellProject作为DDD聚合根，管理数据集、井名映射、项目属性等。

Architecture
------------
层次/依赖: models层聚合根，依赖types、datasets、exceptions
设计原则: DDD聚合根模式、数据一致性、业务边界
性能特征: 内存优化、延迟加载、类型安全

遵循CCG规范：
- CS-2: 使用Wp前缀命名
- TS-1: 完整类型注解覆盖
- EH-4: 异常链保持
- CT-2: 使用枚举常量

References
----------
- 《SCAPE_DDS_详细设计_logwp.md》§4.1 - WpWellProject设计
- 《SCAPE_SAD_软件架构设计.md》§5.1 - 核心类图
"""

from copy import copy
from typing import Any, TYPE_CHECKING
from dataclasses import dataclass, field
from datetime import datetime
import uuid

if TYPE_CHECKING:
    from .datasets.base import WpDepthIndexedDatasetBase

from logwp.models.constants import WpApiKeys, WpStandardColumn, WpDepthUnit, WpLogMessage
from logwp.models.exceptions import (
    WpConsistencyError, WpDataError, WpT2AxisError
)
from logwp.infra.exceptions import ErrorContext
from logwp.models.types import (
    WpDataDict, WpMetaDict,
    WpDatasetName, WpWellName, WpIdentifier
)
from logwp.models.utils import CaseInsensitiveDict, WpStringNormalizer
from .head import WpHead
from .mapping import WpWellMap
from logwp.infra import get_logger
from logwp.models.datasets.bundle import WpDataFrameBundle
from logwp.models.datasets.continuous import WpContinuousDataset
from logwp.models.datasets.discrete import WpDiscreteDataset
from logwp.models.datasets.interval import WpIntervalDataset

# 获取结构化日志记录器
logger = get_logger(__name__)

@dataclass
class WpWellProject:
    """测井项目聚合根（格式无关的通用模型）。

    WpWellProject是logwp包的核心聚合根，管理完整的测井项目数据。
    它体现了logwp包的核心设计哲学：格式无关的测井数据模型。

    Architecture Design Philosophy - 架构设计哲学
    ==========================================

    🎯 **格式无关原则 (Format-Agnostic Principle)**：
    logwp是与数据格式无关的测井数据模型，不绑定任何特定的文件格式。
    - WpWellProject ≠ WP文件的直接映射
    - WpHead ≠ _Head_Info工作表的直接映射
    - WpDataset ≠ 数据工作表的直接映射
    - 支持WP、LAS、JSON、HDF5等多种格式的数据

    🏗️ **架构分层设计 (Layered Architecture)**：
    ```
    应用层：    业务逻辑、算法计算、数据分析
                ↓
    models层：  WpWellProject (格式无关的业务模型)
                ↓
    I/O层：     wp_reader.py  las_reader.py  json_reader.py
                ↓
    存储层：    WP文件       LAS文件       JSON文件
    ```

    📚 **WFS规范的作用 (Role of WFS Specification)**：
    - **设计模板**：WFS提供了测井数据管理的最佳实践和设计思想
    - **主要格式**：WP格式是logwp的主要支持格式，但不是唯一格式
    - **参考标准**：WFS的属性分类、作用域查找等概念被借鉴到通用模型中
    - **不是约束**：logwp的实现不受WFS格式细节约束

    🔧 **设计测井数据模型的思路 (Design Approach)**：

    1. **业务优先 (Business First)**：
       - 先理解测井业务的本质需求（属性管理、数据查找、算法计算）
       - 再考虑如何用代码模型表达这些业务概念
       - 最后才考虑具体的文件格式支持

    2. **抽象与具体分离 (Separation of Abstract and Concrete)**：
       - 抽象层：WpWellProject、WpHead、WpDataset等业务模型
       - 具体层：wp_reader、las_reader等格式解析器
       - 通过依赖注入实现解耦

    3. **借鉴而非复制 (Inspire not Copy)**：
       - 从WFS规范中学习属性管理的分层思想
       - 从LAS规范中学习曲线数据的组织方式
       - 从现代数据科学中学习DataFrame的处理模式
       - 融合各种最佳实践，形成统一的模型

    4. **扩展性设计 (Extensibility Design)**：
       - 支持新的数据格式：只需添加新的reader/writer
       - 支持新的属性类型：通过元数据系统扩展
       - 支持新的业务需求：通过组合模式扩展

    Core Components - 核心组件
    ==========================

    🏛️ **WpHead (属性管理器)**：
    - 格式无关的属性存储和查找
    - 支持作用域查找（dataset、well、curve）
    - 元数据管理（类型、单位、描述）
    - 借鉴WFS的分层属性思想但不绑定格式

    📊 **WpDataset (数据集管理)**：
    - 三种标准类型：Continuous、Point、Interval
    - 统一的数据访问接口
    - GPU/CPU计算支持
    - 自动类型推断和验证

    🗺️ **WpWellMap (井名映射)**：
    - 格式无关的井名标准化
    - 支持多种命名约定
    - 循环映射检测
    - 不区分大小写匹配



    Design Patterns - 设计模式
    ==========================

    🎯 **聚合根模式 (Aggregate Root)**：
    WpWellProject作为聚合根，确保数据一致性和业务规则

    🔄 **适配器模式 (Adapter Pattern)**：
    不同格式的reader/writer适配到统一的模型接口

    Examples - 使用示例
    ===================

    ```python
    # 1. 格式无关的项目创建
    project = WpWellProject(name="Santos_Field")

    # 2. 属性管理（不依赖具体格式）
    project.head.set_attribute("project_version", "1.0")
    project.head.set_attribute("T2_AXIS", t2_config, dataset="NMR_logs")

    # 4. 跨格式数据访问
    t2_axis = project.head.get_attribute("T2_AXIS", dataset="NMR_logs")
    gr_data = project.get_dataset("GR_logs").filter_depth_range(1.0, 2.5)

    # 5. 业务逻辑（格式无关）
    porosity = project.calculate_porosity_from_nmr()
    permeability = project.estimate_permeability_obmiq()
    ```

    Architecture Benefits - 架构优势
    ===============================

    ✅ **格式独立性**：支持多种数据格式，不被单一格式约束
    ✅ **业务聚焦**：专注于测井业务逻辑，而非格式解析细节
    ✅ **扩展性强**：易于添加新格式、新算法、新功能
    ✅ **测试友好**：业务逻辑与I/O分离，便于单元测试
    ✅ **性能优化**：统一的数据模型便于GPU加速和并行计算
    ✅ **类型安全**：现代化类型系统，编译时错误检查

    Architecture
    ------------
    层次/依赖: models层聚合根，格式无关的业务模型
    设计原则: 格式解耦、聚合根模式、数据一致性、业务规则封装
    性能特征: 内存优化、延迟加载、缓存友好、GPU加速支持

    Attributes:
        name: 项目名称
        datasets: 数据集字典，键为数据集名称
        head: 项目头部信息管理器（格式无关）
        well_map: 井名映射管理器（格式无关）
        default_depth_reference_unit: 项目默认深度单位（用户可设定）

        created_at: 创建时间
        modified_at: 修改时间
        project_id: 项目唯一标识

    Examples:
        >>> # 格式无关的项目创建和使用
        >>> project = WpWellProject(name="Santos_Study")
        >>> project.head.set_attribute("version", "1.0")
        >>> project.add_dataset(dataset)
        >>> dataset = project.get_dataset("OBMIQ_logs")
        >>> ml_data = dataset.df  # 直接获取DataFrame用于ML
        >>> wells = project.get_all_wells()

    References
    ==========
    - 《SCAPE_SAD_软件架构设计.md》§5.1.7 - 格式无关架构设计哲学
    - 《SCAPE_DDS_详细设计_logwp.md》§4.1 - WpWellProject聚合根设计
    - Domain-Driven Design (DDD) - 聚合根和领域模型理论
    """

    name: WpIdentifier
    datasets: CaseInsensitiveDict = field(default_factory=CaseInsensitiveDict)
    head: WpHead = field(default_factory=WpHead)
    well_map: WpWellMap = field(default_factory=WpWellMap)
    default_depth_reference_unit: str = field(default=WpDepthUnit.METER)  # 默认使用米作为深度单位

    created_at: datetime = field(default_factory=datetime.now)
    modified_at: datetime = field(default_factory=datetime.now)
    project_id: str = field(default_factory=lambda: str(uuid.uuid4()))

    def __post_init__(self):
        """初始化后处理，支持CIIA架构。"""
        # 如果name是字符串，转换为WpIdentifier
        if isinstance(self.name, str):
            object.__setattr__(self, 'name', WpIdentifier(self.name))

    @classmethod
    def create_with_datasets(
        cls,
        name: str,
        datasets: dict[str, Any],
        *,
        head_attributes: dict[str, Any] | None = None,
        well_mappings: dict[str, str] | None = None,
        default_depth_unit: str = WpDepthUnit.METER
    ) -> 'WpWellProject':
        """创建项目并批量添加数据集的便捷方法。

        一次性创建井工程项目并添加多个数据集，避免逐个调用add_dataset。
        这是常见的业务需求，特别是在批量数据处理和项目初始化时。

        Args:
            name: 项目名称
            datasets: 数据集字典，键为数据集名称，值为数据集对象
            head_attributes: 项目头部属性字典（可选）
            well_mappings: 井名映射字典（可选）
            default_depth_unit: 默认深度单位

        Returns:
            WpWellProject: 创建的项目实例

        Raises:
            WpConsistencyError: 数据集验证失败或名称冲突

        Examples:
            >>> # 批量创建项目和数据集
            >>> datasets = {
            ...     "logs": continuous_dataset,
            ...     "core": discrete_dataset,
            ...     "zones": interval_dataset
            ... }
            >>> project = WpWellProject.create_with_datasets(
            ...     name="Santos_Study",
            ...     datasets=datasets,
            ...     head_attributes={"version": "1.0", "field": "Santos"},
            ...     well_mappings={"C-1A": "C-1", "C-1B": "C-1"}
            ... )
            >>> assert len(project.datasets) == 3
            >>> assert project.head.get_attribute("version") == "1.0"

        Note:
            - 所有数据集必须是有效的数据集对象（具有name和df属性）
            - 数据集名称不能重复
            - 头部属性和井名映射是可选的
        """
        # 创建项目实例
        project = cls(
            name=name,
            default_depth_reference_unit=default_depth_unit
        )

        # 批量添加数据集
        for dataset_name, dataset in datasets.items():
            project.add_dataset(dataset_name, dataset)
            logger.debug(WpLogMessage.DATASET_ADDED_TO_PROJECT,
                        project_name=name,
                        dataset_name=dataset_name,
                        dataset_type=getattr(dataset, 'dataset_type', 'unknown'))

        # 设置头部属性
        if head_attributes:
            for attr_name, attr_value in head_attributes.items():
                project.head.set_attribute(attr_name, attr_value)
            logger.debug(WpLogMessage.PROJECT_HEAD_ATTRIBUTES_SET,
                        project_name=name,
                        attributes_count=len(head_attributes))

        # 设置井名映射
        if well_mappings:
            for source_well, target_well in well_mappings.items():
                project.well_map.add_mapping(source_well, target_well)
            logger.debug(WpLogMessage.WELL_MAPPINGS_SET,
                        project_name=name,
                        mappings_count=len(well_mappings))

        logger.info(WpLogMessage.PROJECT_CREATED_COMPLETE,
                   project_name=name,
                   datasets_count=len(datasets),
                   has_head_attributes=bool(head_attributes),
                   has_well_mappings=bool(well_mappings))

        return project

    def add_dataset(self, name: str | WpDatasetName, dataset: Any) -> None:
        """添加数据集到项目。

        io层调用：add_dataset(name, dataset) - 支持渐进式构造

        Args:
            name: 数据集名称
            dataset: 数据集对象

        Raises:
            WpConsistencyError: 数据集名称冲突或参数无效

        Examples:
            >>> # io层调用（渐进式构造）
            >>> project.add_dataset("OBMIQ_logs", continuous_dataset)

        References:
            《SCAPE_DDS_logwp_io层的渐进式生成流程.md》§3.4.5 - 添加到项目
        """
        # 验证数据集对象
        if not hasattr(dataset, 'name') or not hasattr(dataset, 'df'):
            raise WpConsistencyError(
                "第二个参数必须是数据集对象",
                context=ErrorContext(
                    operation="add_dataset",
                    additional_info={"parameter_type": type(dataset).__name__}
                )
            )

        dataset_name = str(name)

        # 检查名称冲突
        if dataset_name in self.datasets:
            ctx = ErrorContext(
                operation="add_dataset",
                dataset_name=dataset_name,
                additional_info={
                    "existing_dataset_type": self.datasets[dataset_name].dataset_type.value,
                    "new_dataset_type": dataset.dataset_type.value,
                    "project_name": self.name
                }
            )
            raise WpConsistencyError(
                f"数据集名称冲突: {dataset_name}",
                context=ctx
            )

        self.datasets[dataset_name] = dataset
        self.modified_at = datetime.now()

    def get_dataset(self, name: WpDatasetName | WpIdentifier) -> Any | None:
        """获取指定名称的数据集（大小写不敏感）。

        支持CIIA架构的大小写不敏感查找，可以使用任意大小写格式查找数据集。

        Args:
            name: 数据集名称（支持str或WpIdentifier，大小写不敏感）

        Returns:
            数据集实例，如果不存在则返回None

        Examples:
            >>> dataset = project.get_dataset("OBMIQ_logs")
            >>> assert dataset.name == "OBMIQ_logs"
            >>>
            >>> # 大小写不敏感查找
            >>> dataset2 = project.get_dataset("obmiq_LOGS")
            >>> assert dataset2 == dataset  # 同一个数据集
            >>>
            >>> # 不存在的数据集返回None
            >>> missing = project.get_dataset("nonexistent")
            >>> assert missing is None
        """
        if name not in self.datasets:
            return None

        return self.datasets[name]

    def has_dataset(self, name: WpDatasetName | WpIdentifier) -> bool:
        """检查指定名称的数据集是否存在（大小写不敏感）。

        支持CIIA架构的大小写不敏感查找，可以使用任意大小写格式检查数据集。

        Args:
            name: 数据集名称（支持str或WpIdentifier，大小写不敏感）

        Returns:
            bool: 数据集存在返回True，否则返回False

        Examples:
            >>> exists = project.has_dataset("OBMIQ_logs")
            >>> assert exists is True
            >>>
            >>> # 大小写不敏感查找
            >>> exists2 = project.has_dataset("obmiq_LOGS")
            >>> assert exists2 is True  # 同一个数据集
            >>>
            >>> # 不存在的数据集
            >>> missing = project.has_dataset("nonexistent")
            >>> assert missing is False
        """
        return name in self.datasets

    def add_dataframe_bundle(
        self,
        dataset_name: str,
        bundle: WpDataFrameBundle,
        *,
        copy_data: bool = False
    ) -> WpDepthIndexedDatasetBase:
        """
        将一个WpDataFrameBundle作为新数据集添加到项目中。

        此方法会自动检测Bundle的数据类型（连续、离散或区间），
        并创建相应的数据集对象。

        Args:
            dataset_name (str): 要创建的新数据集的名称。
            bundle (WpDataFrameBundle): 包含数据和元数据的Bundle对象。
            copy_data (bool, optional): 是否深度拷贝Bundle中的数据。
                - True: 创建DataFrame和元数据的深拷贝，与原Bundle完全独立。
                - False (默认): 直接使用原Bundle的引用，性能更高但数据共享。

        Returns:
            WpDepthIndexedDatasetBase: 已添加到项目中的新数据集对象。

        Raises:
            WpDataError: 如果同名数据集已存在，或Bundle数据无效。
        """
        if dataset_name in self.datasets:
            raise WpDataError(
                f"无法添加数据集：名称 '{dataset_name}' 已存在于项目中。",
                context=ErrorContext(operation="add_dataframe_bundle", project_name=self.name)
            )

        logger.info(
            "开始从DataFrame Bundle添加新数据集",
            operation="add_dataframe_bundle",
            project_name=self.name,
            new_dataset_name=dataset_name,
            copy_data=copy_data
        )

        # 1. 根据copy_data参数处理数据和元数据
        df_to_add = bundle.data.copy(deep=True) if copy_data else bundle.data
        metadata_to_add = copy.deepcopy(bundle.curve_metadata) if copy_data else bundle.curve_metadata

        # 2. 智能检测数据集类型
        sampling_rate = 0.0
        if bundle.is_interval_bundle:
            dataset_class = WpIntervalDataset
        else:
            try:
                is_uniform, rate = bundle.check_uniform_depth_sampling()
                if is_uniform:
                    dataset_class = WpContinuousDataset
                    sampling_rate = rate if rate is not None else 0.0
                else:
                    dataset_class = WpDiscreteDataset
            except WpDataError as e:
                logger.warning(
                    f"无法确定深度采样均匀性，将数据集 '{dataset_name}' 创建为离散型。原因: {e}",
                    operation="add_dataframe_bundle",
                    project_name=self.name
                )
                dataset_class = WpDiscreteDataset

        # 3. 创建并附加数据到新数据集
        new_dataset = dataset_class.create_empty(dataset_name)
        new_dataset.direct_attach_curves_data(df_to_add, metadata_to_add)

        # 4. 如果是连续型，设置采样率
        if isinstance(new_dataset, WpContinuousDataset):
            new_dataset.depth_sampling_rate = sampling_rate

        # 5. 将新数据集添加到项目中
        self.add_dataset(dataset_name, new_dataset)

        logger.info(
            f"成功添加 '{dataset_name}' ({dataset_class.__name__}) 数据集到项目中。",
            operation="add_dataframe_bundle",
            project_name=self.name
        )
        return new_dataset

    def apply_well_mapping(
        self,
        dataset_names: str | list[str] | None = None,
        *,
        create_new: bool = False,
        new_dataset_names: str | list[str] | None = None
    ) -> dict[str, str] | None:
        """执行井名映射操作。

        根据项目的well_map配置，对指定数据集执行井名映射。支持就地修改
        和克隆新数据集两种操作模式。

        Args:
            dataset_names: 要映射的数据集名称
                - None: 对所有数据集进行映射
                - str: 对单个数据集进行映射
                - list[str]: 对指定的多个数据集进行映射
            create_new: 操作模式选择
                - False: 就地修改原数据集（默认）
                - True: 克隆新数据集并映射
            new_dataset_names: 新数据集名称（仅在create_new=True时有效）
                - None: 自动生成唯一名称
                - str: 单个新数据集名称
                - list[str]: 多个新数据集名称（必须与dataset_names一一对应）

        Returns:
            dict[str, str] | None:
                - create_new=False: 返回None
                - create_new=True: 返回{原数据集名称: 新数据集名称}映射字典

        Raises:
            WpDatasetNotFoundError: 指定的数据集不存在
            WpConsistencyError: 新数据集名称与现有数据集冲突
            ValueError: 参数数量不匹配或参数无效

        Examples:
            >>> # 就地映射所有数据集
            >>> project.apply_well_mapping()

            >>> # 就地映射指定数据集
            >>> project.apply_well_mapping("OBMIQ_logs")
            >>> project.apply_well_mapping(["OBMIQ_logs", "NMR_logs"])

            >>> # 克隆并映射，自动生成名称
            >>> result = project.apply_well_mapping(
            ...     dataset_names="OBMIQ_logs",
            ...     create_new=True
            ... )
            >>> # 返回: {"OBMIQ_logs": "OBMIQ_logs_1"}

            >>> # 克隆并映射，指定新名称
            >>> result = project.apply_well_mapping(
            ...     dataset_names=["OBMIQ_logs", "NMR_logs"],
            ...     create_new=True,
            ...     new_dataset_names=["OBMIQ_mapped", "NMR_mapped"]
            ... )
            >>> # 返回: {"OBMIQ_logs": "OBMIQ_mapped", "NMR_logs": "NMR_mapped"}
        """
        # 转发到服务层，所有复杂逻辑在服务层处理
        from logwp.models.internal import well_mapping

        return well_mapping.apply_well_mapping(
            project=self,
            dataset_names=dataset_names,
            create_new=create_new,
            new_dataset_names=new_dataset_names
        )

    def generate_unique_dataset_name(self, preferred_name: str | WpDatasetName) -> str:
        """生成唯一的数据集名称。

        如果指定名称不存在则直接返回，否则在后面添加数字后缀(_1, _2, ...)
        直到找到不冲突的名称。

        Args:
            preferred_name: 用户期望的数据集名称

        Returns:
            str: 唯一的数据集名称

        Examples:
            >>> unique_name = project.generate_unique_dataset_name("OBMIQ_logs")
            >>> # 如果"OBMIQ_logs"不存在，返回"OBMIQ_logs"
            >>> # 如果存在，返回"OBMIQ_logs_1"、"OBMIQ_logs_2"等
            >>>
            >>> # 大小写不敏感检查
            >>> unique_name = project.generate_unique_dataset_name("obmiq_LOGS")
            >>> # 如果项目中已有"OBMIQ_logs"，会返回"obmiq_LOGS_1"
        """
        base_name = str(preferred_name)

        # 如果首选名称不存在，直接返回
        if not self.has_dataset(base_name):
            return base_name

        # 否则尝试添加数字后缀
        counter = 1
        while True:
            candidate_name = f"{base_name}_{counter}"
            if not self.has_dataset(candidate_name):
                return candidate_name
            counter += 1

    def extract_curves(
        self,
        source_dataset: str,
        target_dataset: str,
        curve_names: list[str] | None = None,
        query_condition: str | None = None
    ) -> WpDepthIndexedDatasetBase:
        """从指定数据集中提取曲线生成新数据集。

        根据查询条件从源数据集中筛选数据，提取指定曲线生成新数据集。
        新数据集自动包含必需的井名列和深度列，遵循WFS规范。

        Args:
            source_dataset: 源数据集名称
            target_dataset: 目标数据集名称（用于新数据集的名称）
            curve_names: 要提取的曲线名称列表（紧凑格式）。如果为 `None`，则提取所有数据曲线
                （即排除井名、深度等系统曲线）。
                - 支持一维曲线：["GR", "DEN", "PHIT"]
                - 支持二维组合曲线基础名称：["T2_VALUE"] 会自动展开为所有元素
                - 也支持二维组合曲线元素：["T2_VALUE[1]", "T2_VALUE[2]"]
                - 自动展开：["GR", "T2_VALUE"] → ["GR", "T2_VALUE[1]", "T2_VALUE[2]", ...]
            query_condition: 查询条件（可选）
                - 使用DataFrame.query()语法
                - 曲线名必须使用${curve_name}占位符格式
                - 例如："${GR} > 50 and ${DEN} < 2.5"

        Returns:
            WpDepthIndexedDatabaseBase: 提取生成的新数据集

        Raises:
            WpDatasetNotFoundError: 源数据集不存在
            WpCurveMetadataError: 曲线名称无效或查询条件语法错误
            ValueError: 参数无效

        Examples:
            >>> # 基本曲线提取
            >>> selected_dataset = project.extract_curves(
            ...     source_dataset="OBMIQ_logs",
            ...     target_dataset="selected_logs",
            ...     curve_names=["GR", "DEN", "PHIT"]
            ... )

            >>> # 带条件的曲线提取
            >>> high_gr_dataset = project.extract_curves(
            ...     source_dataset="OBMIQ_logs",
            ...     target_dataset="high_gr_logs",
            ...     curve_names=["GR", "DEN", "PHIT", "T2_VALUE[1]"],
            ...     query_condition="${GR} > 50 and ${DEN} < 2.5"
            ... )

            >>> # 深度范围筛选
            >>> shallow_dataset = project.extract_curves(
            ...     source_dataset="OBMIQ_logs",
            ...     target_dataset="shallow_logs",
            ...     curve_names=["GR", "PHIT"],
            ...     query_condition="${MD} < 2000"
            ... )

            >>> # 如需添加到项目中，可手动添加
            >>> project.add_dataset("selected_logs", selected_dataset)

        Note:
            - 新数据集自动包含井名列和深度列，无需在curve_names中指定
            - 使用clone_dataset()方法确保数据完全独立
            - 查询条件中的曲线名会自动转换为DataFrame友好格式
            - 支持大小写不敏感的曲线名称查找
            - 返回的数据集不会自动添加到项目中，需要手动添加

        References:
            《SCAPE_DDS_logwp_曲线提取.md》§2.1 - 核心接口设计
        """
        from logwp.models.datasets.internal import curve_extraction

        # 直接转发到服务层，返回生成的数据集
        return curve_extraction.extract_curves(
            project=self,
            source_dataset=source_dataset,
            target_dataset=target_dataset,
            curve_names=curve_names,
            query_condition=query_condition
        )


    def merge_datasets_via_continuous(
        self,
        left_dataset: str,
        left_curves: list[str],
        right_dataset: str,
        right_curves: list[str],
        *,
        left_query: str | None = None,
        right_query: str | None = None,
        merge_sampling_interval: float | None = None,
        interpolation_method: str = "nearest",
        result_mode: str = "full",
        new_dataset_name: str | None = None
    ) -> WpDepthIndexedDatasetBase:
        """通过连续型转换方式合并两个数据集（SQL FULL JOIN语义）。

        **SQL FULL JOIN语义**：
        - 包含左右数据集的所有井
        - 每口井在自己的深度范围内进行合并
        - 避免生成跨井的无意义深度点
        - 左数据集独有的井：保留左数据集曲线，右数据集曲线为NaN
        - 右数据集独有的井：保留右数据集曲线，左数据集曲线为NaN
        - 共同井：在深度范围并集内合并两个数据集的曲线

        将左右数据集都转换为连续型数据集进行合并，最终结果类型根据
        result_mode和深度采样特征智能确定（continuous或discrete）。
        支持不同类型数据集的智能转换和曲线名冲突自动处理。

        **设计模式**：遵循SAD文档§4.12 Utility/Helper Pattern
        - 核心类专注接口和状态管理
        - 复杂业务逻辑转发给服务层处理
        - 保持方法签名简洁和用户友好

        Args:
            left_dataset: 左数据集名称
            left_curves: 左数据集要提取的曲线列表（紧凑格式）
            left_query: 左数据集查询条件（可选，使用extract_curves语法）
            right_dataset: 右数据集名称
            right_curves: 右数据集要提取的曲线列表（紧凑格式）
            right_query: 右数据集查询条件（可选，使用extract_curves语法）
            merge_sampling_interval: 合并后的深度采样间隔，None表示自动确定
            interpolation_method: 插值方法，默认"nearest"
            result_mode: 结果模式，"full"保留所有深度点，"compact"移除全空行
            new_dataset_name: 新数据集名称，None表示自动生成

        Returns:
            WpDepthIndexedDatabaseBase: 合并后的数据集（continuous或discrete类型）

        Raises:
            WpDatasetNotFoundError: 源数据集不存在
            WpValidationError: 深度单位不一致或参数无效
            WpDataError: 数据集状态异常或合并失败

        Note:
            - 左数据集曲线名保持不变，右数据集重名曲线自动改名（如GR→GR_1）
            - 深度单位必须一致，否则抛出异常
            - 自动确定采样间隔算法：
              * 两个continuous：取最小采样间隔
              * 一个discrete：取continuous和discrete的calculate_depth_sampling_rate最小值
              * 一个interval：取另一个数据集的采样间隔
            - 合并深度范围为两个数据集深度范围的并集
            - result_mode="compact"时会检查是否为等间隔采样，决定返回continuous或discrete
            - **严格遵循CDP-1规范**：返回的数据集DataFrame必须使用默认整数索引（RangeIndex）

        Warning:
            此方法会通过重采样改变原始深度值，适用于需要统一深度
            采样间隔的合并场景。如需保持原始深度值，请考虑其他合并方法。

        Examples:
            >>> # 基本合并
            >>> merged_dataset = project.merge_datasets_via_continuous(
            ...     left_dataset="logs",
            ...     left_curves=["GR", "DEN", "PHIT"],
            ...     right_dataset="core",
            ...     right_curves=["PERM", "GR"],  # GR会被重命名为GR_1
            ...     merge_sampling_interval=0.5
            ... )

            >>> # 带查询条件的合并
            >>> filtered_merged = project.merge_datasets_via_continuous(
            ...     left_dataset="logs",
            ...     left_curves=["GR", "DEN"],
            ...     left_query="${GR} > 50",
            ...     right_dataset="nmr",
            ...     right_curves=["T2_VALUE", "PHIT"],
            ...     right_query="${PHIT} > 0.1",
            ...     result_mode="compact"
            ... )

            >>> # 如需添加到项目中，可手动添加
            >>> project.add_dataset("merged_data", merged_dataset)

        References:
            《SCAPE_DDS_logwp_数据集合并.md》§2.1 - 核心接口设计
        """
        # 转发到服务层（遵循Utility/Helper Pattern）
        from logwp.models.internal import dataset_merge

        return dataset_merge.merge_datasets_via_continuous(
            project=self,
            left_dataset=left_dataset,
            left_curves=left_curves,
            left_query=left_query,
            right_dataset=right_dataset,
            right_curves=right_curves,
            right_query=right_query,
            merge_sampling_interval=merge_sampling_interval,
            interpolation_method=interpolation_method,
            result_mode=result_mode,
            new_dataset_name=new_dataset_name
        )

    def merge_datasets_left_aligned(
        self,
        left_dataset: str,
        left_curves: list[str],
        right_dataset: str,
        right_curves: list[str],
        *,
        left_query: str | None = None,
        right_query: str | None = None,
        interpolation_method: str = "nearest",
        new_dataset_name: str | None = None
    ) -> WpDepthIndexedDatasetBase:
        """依左侧数据集深度索引进行数据集合并（SQL LEFT JOIN语义）。

        **SQL LEFT JOIN语义**：
        - 只包含左数据集的井和深度点
        - 以左数据集的井名和深度索引为基准
        - 右数据集在左数据集的深度点上进行插值
        - 右数据集没有的井：对应曲线填充NaN
        - 右数据集没有的深度点：对应曲线填充NaN
        - 保持左数据集的完整结构不变

        以左数据集的深度索引作为基准，将右数据集转换为兼容格式后进行合并。
        最终结果保持左数据集的深度结构和类型，适用于需要保持原始深度值的场景。

        **设计模式**：遵循SAD文档§4.12 Utility/Helper Pattern
        - 核心类专注接口和状态管理
        - 复杂业务逻辑转发给服务层处理
        - 保持方法签名简洁和用户友好

        Args:
            left_dataset: 左数据集名称（必须为continuous或discrete类型）
            left_curves: 左数据集要提取的曲线列表（紧凑格式）
            left_query: 左数据集查询条件（可选，使用extract_curves语法）
            right_dataset: 右数据集名称（无类型限制）
            right_curves: 右数据集要提取的曲线列表（紧凑格式）
            right_query: 右数据集查询条件（可选，使用extract_curves语法）
            interpolation_method: 插值方法，默认"nearest"
            new_dataset_name: 新数据集名称，None表示自动生成

        Returns:
            WpDepthIndexedDatabaseBase: 合并后的数据集（与左数据集类型相同）

        Raises:
            WpDatasetNotFoundError: 源数据集不存在
            WpValidationError: 深度单位不一致或左数据集类型不支持
            WpDataError: 数据集状态异常或合并失败

        Note:
            - 左数据集曲线名保持不变，右数据集重名曲线自动改名（如GR→GR_1）
            - 深度单位必须一致，否则抛出异常
            - 左数据集必须为continuous或discrete类型，interval类型会抛出异常
            - 右数据集无类型限制，会自动转换为与左数据集兼容的格式
            - 合并方式为左连接：以左数据集深度为准，右数据集没有对应深度点的值置空
            - 最终数据集类型与左数据集相同（continuous→continuous，discrete→discrete）
            - **严格遵循CDP-1规范**：返回的数据集DataFrame必须使用默认整数索引（RangeIndex）

        Warning:
            此方法保持左数据集的原始深度值，适用于需要保持深度结构的合并场景。
            如需统一深度采样间隔，请使用merge_datasets_via_continuous方法。

        Examples:
            >>> # 基本合并（保持左数据集深度结构）
            >>> merged_dataset = project.merge_datasets_left_aligned(
            ...     left_dataset="logs",  # continuous类型
            ...     left_curves=["GR", "DEN", "PHIT"],
            ...     right_dataset="core",  # discrete类型
            ...     right_curves=["PERM", "GR"],  # GR会被重命名为GR_1
            ... )
            >>> # 结果为continuous类型，深度序列与logs数据集完全相同

            >>> # 带查询条件的合并
            >>> filtered_merged = project.merge_datasets_left_aligned(
            ...     left_dataset="core",  # discrete类型
            ...     left_curves=["PERM", "FACIES"],
            ...     left_query="${PERM} > 10",
            ...     right_dataset="nmr",  # continuous类型
            ...     right_curves=["T2_VALUE", "T2LM"],
            ...     right_query="${T2LM} > 10",
            ...     interpolation_method="linear"
            ... )
            >>> # 结果为discrete类型，深度点与core数据集完全相同

        References:
            《SCAPE_DDS_logwp_依左侧进行数据集合并.md》§2.1 - 核心接口设计
        """
        # 转发到服务层（遵循Utility/Helper Pattern）
        from logwp.models.internal import dataset_left_aligned_merge

        return dataset_left_aligned_merge.merge_datasets_left_aligned(
            project=self,
            left_dataset=left_dataset,
            left_curves=left_curves,
            left_query=left_query,
            right_dataset=right_dataset,
            right_curves=right_curves,
            right_query=right_query,
            interpolation_method=interpolation_method,
            new_dataset_name=new_dataset_name
        )

    def dropna_dataset(
        self,
        source_dataset_name: str,
        curve_names: list[str],
        new_dataset_name: str,
        *,
        dropna_how: str = "any"
    ) -> WpDepthIndexedDatasetBase:
        """
        对指定数据集的特定曲线执行dropna操作，并生成一个新的数据集。

        此方法允许用户清理数据集中包含NaN值的行，并根据清理后的数据特征
        智能地确定新数据集的类型。

        Args:
            source_dataset_name (str): 源数据集的名称。
            curve_names (list[str]): 用于检查NaN值的曲线名称列表。如果为空列表，
                则对所有数据曲线（不包括井名、深度等系统曲线）进行操作。
            new_dataset_name (str): 清理后生成的新数据集的名称。
            dropna_how (str, optional): dropna的策略，默认为 "any"。
                - "any": 如果指定曲线中任意一个为NaN，则删除该行。
                - "all": 只有当指定曲线全部为NaN时，才删除该行。

        Returns:
            WpDepthIndexedDatasetBase: 一个新的、经过清理的数据集实例。

        Raises:
            WpDatasetNotFoundError: 如果源数据集不存在。
            WpCurveMetadataError: 如果指定的曲线在数据集中不存在。
            ValueError: 如果 `dropna_how` 参数无效。
        """
        from logwp.models.datasets.internal import dataset_dropna

        return dataset_dropna.dropna_dataset(
            project=self,
            source_dataset_name=source_dataset_name,
            curve_names=curve_names,
            new_dataset_name=new_dataset_name,
            dropna_how=dropna_how
        )

    def to_dict(self) -> dict[str, Any]:
        """转换为字典格式。

        Returns:
            dict[str, Any]: 项目信息字典，使用枚举常量作为键名

        Examples:
            >>> project_dict = project.to_dict()
            >>> assert project_dict[WpApiKeys.DATA]["name"] == "Santos_Study"
        """
        dataset_info = []
        for dataset in self.datasets.values():
            dataset_dict = dataset.to_dict()
            dataset_info.append(dataset_dict)

        return {
            WpApiKeys.IS_VALID.value: True,
            WpApiKeys.DATA.value: {
                "project_id": self.project_id,
                "name": self.name,
                "created_at": self.created_at.isoformat(),
                "modified_at": self.modified_at.isoformat(),
                "dataset_count": len(self.datasets),
                "well_count": len(self.get_all_wells()),
                "datasets": dataset_info
            },
            WpApiKeys.META.value: {
                "version": "1.0",
                "created_by": "WpWellProject",
                "created_at": datetime.now(),
                "attributes": {
                    "has_well_mapping": len(self.well_map.mappings) > 0,
                    "has_head_info": len(self.head.attributes) > 0,
                    "has_source_meta": False  # 已移除WpSourceMeta过度设计
                }
            }
        }

    def convert_dataset_depth_reference_unit(
        self,
        dataset_names: list[str] | None,
        target_unit: str
    ) -> None:
        """转换指定数据集的深度参考曲线单位。

        Args:
            dataset_names: 要转换的数据集名称列表，None表示转换所有数据集
            target_unit: 目标深度单位（"m" 或 "ft"）

        Raises:
            WpValidationError: 当目标单位不支持时抛出
            WpDataError: 当数据集转换失败时抛出

        Note:
            - 批量处理多个数据集的深度单位转换
            - 跳过不存在或不支持深度转换的数据集
            - 具体实现委托给service层

        Examples:
            >>> # 转换指定数据集为英尺
            >>> project.convert_dataset_depth_reference_unit(["logs", "core"], "ft")

            >>> # 转换所有数据集为米
            >>> project.convert_dataset_depth_reference_unit(None, "m")

        References:
            《SCAPE_DDS_详细设计_logwp.md》§4.2 - 深度单位管理系统
        """
        from logwp.models.internal.depth_unit_conversion import convert_project_datasets_depth_unit
        convert_project_datasets_depth_unit(self, dataset_names, target_unit)

    def convert_dataset_depth_reference_unit_to_default(
        self,
        dataset_names: list[str] | None = None
    ) -> None:
        """转换指定数据集的深度参考曲线单位为默认单位。

        Args:
            dataset_names: 要转换的数据集名称列表，None表示转换所有数据集

        Raises:
            WpValidationError: 当默认单位不支持时抛出
            WpDataError: 当数据集转换失败时抛出

        Note:
            - 使用default_depth_reference_unit作为目标单位
            - 其他行为与convert_dataset_depth_reference_unit相同
            - 具体实现委托给service层

        Examples:
            >>> # 设置默认单位
            >>> project.default_depth_reference_unit = "ft"

            >>> # 转换指定数据集为默认单位
            >>> project.convert_dataset_depth_reference_unit_to_default(["logs"])

            >>> # 转换所有数据集为默认单位
            >>> project.convert_dataset_depth_reference_unit_to_default()

        References:
            《SCAPE_DDS_详细设计_logwp.md》§4.2 - 深度单位管理系统
        """
        from logwp.models.internal.depth_unit_conversion import convert_project_datasets_depth_unit_to_default
        convert_project_datasets_depth_unit_to_default(self, dataset_names)

    def generate_data_summary(
        self,
        output_path: str | None = None,
        format: str = "markdown",
        template: str = "default"
    ) -> str:
        """生成测井数据概况报告。

        生成包含井头信息、井名映射、数据集概况和曲线统计的完整数据概况。
        支持JSON结构化数据和Markdown人类可读报告两种格式。

        Args:
            output_path: 输出文件路径，None表示自动命名
            format: 输出格式，"json"为结构化数据，"markdown"为人类可读报告
            template: 模板名称，仅在format="markdown"时有效，可选值："default", "english"

        Returns:
            str: 生成的报告文件路径

        Raises:
            OSError: 文件写入失败
            ValueError: 不支持的格式

        Examples:
            >>> # 生成Markdown报告（默认）
            >>> report_path = project.generate_data_summary()
            >>> print(f"报告已生成: {report_path}")
            >>>
            >>> # 生成JSON结构化数据
            >>> json_path = project.generate_data_summary(format="json")
            >>> print(f"JSON数据已生成: {json_path}")
            >>>
            >>> # 生成英文报告
            >>> en_path = project.generate_data_summary(format="markdown", template="english")
            >>> print(f"英文报告已生成: {en_path}")
            >>>
            >>> # 指定文件名
            >>> report_path = project.generate_data_summary("my_summary.md", "markdown", "default")
            >>> print(f"报告已生成: {report_path}")

        Note:
            - JSON格式：纯结构化数据，便于程序化处理和API集成
            - Markdown格式：人类可读报告，包含中文说明和格式化表格
            - 数据与展示分离：核心数据结构化，展示通过模板生成
            - 支持按数据类型分类进行曲线统计分析
            - 支持按井统计深度范围和数据行数

        References:
            《SCAPE_DDS_logwp_generate_summary.md》§4.1 - 主入口方法设计
        """
        from typing import Literal
        from logwp.models.internal.summary_service import generate_project_summary_report

        # 类型检查
        if format not in ("json", "markdown"):
            raise ValueError(f"不支持的格式: {format}，支持的格式: 'json', 'markdown'")

        return generate_project_summary_report(self, output_path, format, template)
