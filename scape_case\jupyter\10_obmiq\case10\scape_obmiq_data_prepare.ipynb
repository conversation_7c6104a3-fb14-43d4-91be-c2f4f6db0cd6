%load_ext autoreload
%autoreload 2

# 导入logwp包用于读取WP文件
from pathlib import Path

import numpy as np
import pandas as pd

from logwp.io import WpExcelReader, WpExcelWriter
from logwp.models.well_project import WpWellProject

# 设置pandas显示选项
pd.set_option('display.max_columns', None)
pd.set_option('display.width', None)
pd.set_option('display.max_colwidth', 50)

print("库导入完成!")

data_file_path = "./santos_data_v1.wp.xlsx" #原始数据
reader = WpExcelReader()

try:
    project = reader.read(data_file_path)
    project.apply_well_mapping()
    print("✅ 成功读取原始wp文件")
    print(f"📊 项目名称: {project.name}")
    print(f"📅 创建时间: {project.created_at}")
    print(f"🔧 默认深度单位: {project.default_depth_reference_unit}")
except Exception as e:
    print(f"❌ 读取原始wp文件失败: {e}")
    project = None

# 定义要提取的曲线列表
# obmiq_curves = [
#    'DEN', 'CN', 'DT', 'RD_LOG10', 'RS_LOG10', 'DRES',
#    'T2LM', 'T2LM_LONG', 'T2_P50', 'T2_P20',
#    'PHIT_NMR', 'PHIE_NMR', 'BFV_NMR', 'BVI_NMR','SWB_NMR', 'SWI_NMR', 'SDR_PROXY',
#    'PHI_T2_DIST_CUM', 'DT2_P50', 'DPHIT_NMR'
#]

#log_scout分析结果
obmiq_curves = [
    'DEN', 'CN', 'DT', 'RD_LOG10', 'RS_LOG10', 'DRES',
    'T2LM_LOG10', 'T2LM_LONG_LOG10', 'T2_P50_LOG10', 'T2_P20_LOG10',
    'PHIT_NMR', 'PHIE_NMR', 'BFV_NMR', 'BVI_NMR','FFV_NMR','SWB_NMR', 'SWI_NMR', 'SFF_NMR',
    'VMICRO', 'VMESO', 'VMACRO', 'SMICRO', 'SMESO', 'SMACRO',
    'LT2STDDEV_FFI', 'LSKEW_FFI', 'LKURT_FFI', 'SDR_PROXY',
    'PHI_T2_DIST_CUM', 'DT2_P50', 'DPHIT_NMR'
]

obmiq_curves_dropna = [
    'DEN', 'CN', 'DT', 'RD_LOG10', 'RS_LOG10', 'DRES',
    'T2LM_LOG10', 'T2LM_LONG_LOG10', 'T2_P50_LOG10', 'T2_P20_LOG10',
    'PHIT_NMR', 'PHIE_NMR', 'BFV_NMR', 'BVI_NMR','FFV_NMR','SWB_NMR', 'SWI_NMR', 'SFF_NMR',
    'VMICRO', 'VMESO', 'VMACRO', 'SMICRO', 'SMESO', 'SMACRO',
    'LT2STDDEV_FFI', 'LSKEW_FFI', 'LKURT_FFI', 'SDR_PROXY',
    'PHI_T2_DIST_CUM'
]

print(f"📊 准备提取OBMIQ相关曲线，共{len(obmiq_curves)}条曲线")
print(f"曲线列表: {', '.join(obmiq_curves)}")
print(f"📊 准备提取OBMIQ相关曲线（dropna），共{len(obmiq_curves_dropna)}条曲线")
print(f"曲线列表: {', '.join(obmiq_curves_dropna)}")

if project is not None:
    from logwp.io.wp_excel.wp_excel_writer import WpExcelWriter
    from logwp.models.well_project import WpWellProject

    print("🔧 开始提取OBMIQ数据集(训练)...")

    try:
        # 1. 提取C-1井的数据
        print("\n📍 提取C-1井数据(训练)...")
        c1_dataset = project.extract_curves(
            source_dataset="logs",
            target_dataset="nmr_obmiq",
            curve_names=obmiq_curves,
            query_condition="${WELL_NO} == 'C-1' and DS_F == 1 "
        )
        project.add_dataset("nmr_obmiq_c_1", c1_dataset)
        c1_dataset = project.dropna_dataset(
            source_dataset_name="nmr_obmiq_c_1",
            curve_names=obmiq_curves,
            new_dataset_name="nmr_obmiq_cleaned",
            dropna_how="any"
        )

        # 创建临时项目并保存
        temp_project_c1 = WpWellProject(name="Santos_OBMIQ_C1")
        temp_project_c1.add_dataset("nmr_obmiq", c1_dataset)

        writer = WpExcelWriter()
        c1_path = "santos_obmiq_cum_c1.wp.xlsx"
        writer.write(temp_project_c1, c1_path, apply_formatting=False)

        print(f"✅ C-1井数据已保存: {c1_path}")
        print(f"   数据形状: {c1_dataset.df.shape}")
        print(f"   数据集类型: {type(c1_dataset).__name__}")

    except Exception as e:
        print(f"❌ C-1井数据提取失败: {e}")
        import traceback
        traceback.print_exc()

    try:
        # 2. 提取T-1井的数据
        print("\n📍 提取T-1井数据(训练)...")
        t1_dataset = project.extract_curves(
            source_dataset="logs",
            target_dataset="nmr_obmiq",
            curve_names=obmiq_curves,
            query_condition="${WELL_NO} == 'T-1' and DS_F == 1 "
        )
        project.add_dataset("nmr_obmiq_t_1", t1_dataset)
        t1_dataset = project.dropna_dataset(
            source_dataset_name="nmr_obmiq_t_1",
            curve_names=obmiq_curves,
            new_dataset_name="nmr_obmiq_cleaned",
            dropna_how="any"
        )

        # 创建临时项目并保存
        temp_project_t1 = WpWellProject(name="Santos_OBMIQ_T1")
        temp_project_t1.add_dataset("nmr_obmc", t1_dataset)

        writer = WpExcelWriter()
        t1_path = "santos_obmiq_cum_t1.wp.xlsx"
        writer.write(temp_project_t1, t1_path, apply_formatting=False)

        print(f"✅ T-1井数据已保存: {t1_path}")
        print(f"   数据形状: {t1_dataset.df.shape}")
        print(f"   数据集类型: {type(t1_dataset).__name__}")

    except Exception as e:
        print(f"❌ T-1井数据提取失败: {e}")
        import traceback
        traceback.print_exc()

    try:
        # 3. 提取所有井的数据
        print("\n📍 提取所有井数据(训练)...")
        all_dataset = project.extract_curves(
            source_dataset="logs",
            target_dataset="nmr_obmiq",
            curve_names=obmiq_curves,
            query_condition="DS_F == 1"
        )
        project.add_dataset("nmr_obmiq_all", all_dataset)
        all_dataset = project.dropna_dataset(
            source_dataset_name="nmr_obmiq_all",
            curve_names=obmiq_curves,
            new_dataset_name="nmr_obmiq_cleaned",
            dropna_how="any"
        )

        # 创建临时项目并保存
        temp_project_all = WpWellProject(name="Santos_OBMIQ_All")
        temp_project_all.add_dataset("nmr_obmiq", all_dataset)

        writer = WpExcelWriter()
        all_path = "santos_obmiq_cum_all.wp.xlsx"
        writer.write(temp_project_all, all_path, apply_formatting=True)

        print(f"✅ 所有井数据已保存: {all_path}")
        print(f"   数据形状: {all_dataset.df.shape}")
        print(f"   数据集类型: {type(all_dataset).__name__}")

        # 显示井名统计
        if 'WELL_NO' in all_dataset.df.columns:
            well_counts = all_dataset.df['WELL_NO'].value_counts()
            print(f"   井名分布: {dict(well_counts)}")

    except Exception as e:
        print(f"❌ 所有井数据提取失败: {e}")
        import traceback
        traceback.print_exc()


    try:
        # 4. 提取所有井的数据(预测)
        print("\n📍 提取所有井数据(预测)...")
        all_apply_dataset = project.extract_curves(
            source_dataset="logs",
            target_dataset="nmr_obmiq_all_apply",
            curve_names=obmiq_curves #需要包含真值，这样方便对比
        )
        project.add_dataset("nmr_obmiq_all_apply", all_apply_dataset)
        all_apply_dataset = project.dropna_dataset(
            source_dataset_name="nmr_obmiq_all_apply",
            curve_names=obmiq_curves_dropna, # dropna时不考虑真值
            new_dataset_name="nmr_obmiq_all_apply_cleaned",
            dropna_how="any"
        )

        # 创建临时项目并保存
        temp_project_all = WpWellProject(name="Santos_OBMIQ_All_Apply")
        temp_project_all.add_dataset("nmr_obmiq_apply", all_apply_dataset)

        writer = WpExcelWriter()
        all_path = "santos_obmiq_cum_all_apply.wp.xlsx"
        writer.write(temp_project_all, all_path, apply_formatting=True)
        report_path = temp_project_all.generate_data_summary(
            format="markdown",
            template="default",
            output_path="santos_obmiq_cum_all_apply_report.md"
        )

        print(f"✅ 所有井数据(预测）已保存: {all_path}")
        print(f"   数据形状: {all_apply_dataset.df.shape}")
        print(f"   数据集类型: {type(all_apply_dataset).__name__}")


    except Exception as e:
        print(f"❌ 所有井数据(预测）提取失败: {e}")
        import traceback
        traceback.print_exc()

    print("\n🎉 OBMIQ数据集提取完成！")

else:
    print("⚠️ 项目未成功加载，跳过OBMIQ数据集提取")