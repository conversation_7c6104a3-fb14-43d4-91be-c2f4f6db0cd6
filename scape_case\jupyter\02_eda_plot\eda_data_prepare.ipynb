%load_ext autoreload
%autoreload 2

# 导入logwp包用于读取WP文件
from pathlib import Path

import numpy as np
import pandas as pd

from logwp.io import WpExcelReader, WpExcelWriter
from logwp.models.well_project import WpWellProject

# 设置pandas显示选项
pd.set_option('display.max_columns', None)
pd.set_option('display.width', None)
pd.set_option('display.max_colwidth', 50)

print("库导入完成!")

data_file_path = "./santos_data_v2.wp.xlsx" #原始数据，使用精校过的数据
reader = WpExcelReader()

try:
    project = reader.read(data_file_path)
    project.apply_well_mapping()
    print("✅ 成功读取原始wp文件")
    print(f"📊 项目名称: {project.name}")
    print(f"📅 创建时间: {project.created_at}")
    print(f"🔧 默认深度单位: {project.default_depth_reference_unit}")
except Exception as e:
    print(f"❌ 读取原始wp文件失败: {e}")
    project = None


# 不含二维
logs_curves = [
    'GR', 'BS', 'CAL', 'DEN', 'CN', 'DT', 'PE',
    'RD', 'RS', 'RD_LOG10', 'RS_LOG10', 'DRES',
    'RD_LWD', 'RS_LWD', 'RD_LWD_LOG10', 'RS_LWD_LOG10', 'DRES_LWD',
    'PHIT_NMR', 'PHIE_NMR', 'CBW_NMR', 'BFV_NMR', 'BVI_NMR', 'FFV_NMR', 'SWB_NMR', 'SWI_NMR', 'SFF_NMR',
    'VMICRO', 'VMESO', 'VMACRO', 'SMICRO', 'SMESO', 'SMACRO',
    'VMICRO_LWD', 'VMESO_LWD', 'VMACRO_LWD',
    'T2CUTOFF', 'T2LM', 'T2LM_LOG10', 'T2LM_LONG', 'T2LM_LONG_LOG10',
    'T2_P20', 'T2_P20_LOG10', 'T2_P50', 'T2_P50_LOG10',
    'LT2STDDEV_FFI', 'LSKEW_FFI', 'LKURT_FFI', 'SDR_PROXY',
    'DT2LM', 'DT2LM_LONG', 'DT2_P50', 'DT2_P20', 'DPHIT_NMR', 'DPHIE_NMR'
]

k_label_curves = [
     'K_LABEL', 'K_LABEL_TYPE', 'PZI'
]

if project is not None:
    from logwp.io.wp_excel.wp_excel_writer import WpExcelWriter
    from logwp.models.well_project import WpWellProject


    try:

        nmr_ternary_ds = project.extract_curves(
            source_dataset="logs",
            target_dataset="nmr_ternary",
            curve_names=['VMICRO','VMESO','VMACRO'],
            query_condition="DS_F == 1"
        )
        project.add_dataset("nmr_ternary", nmr_ternary_ds)
        nmr_ternary_ds = project.dropna_dataset(
            source_dataset_name="nmr_ternary",
            curve_names=[],
            new_dataset_name="nmr_ternary_cleaned",
            dropna_how="any"
        )
        project.add_dataset("nmr_ternary_cleaned", nmr_ternary_ds)

        merge_ds = project.merge_datasets_left_aligned(
            left_dataset="nmr_ternary_cleaned",
            left_curves=['VMICRO','VMESO','VMACRO'],
            right_dataset="K_Label",
            right_curves=k_label_curves,
            new_dataset_name="nmr_ternary_k"
        )


        # 创建临时项目并保存
        temp_project = WpWellProject(name="nmr_ternary")
        temp_project.add_dataset("nmr_ternary", merge_ds)

        writer = WpExcelWriter()
        out_path = "nmr_ternary.wp.xlsx"
        writer.write(temp_project, out_path, apply_formatting=True)

        print(f"✅数据已保存: {out_path}")

    except Exception as e:
        print(f"❌ 数据提取失败: {e}")
        import traceback
        traceback.print_exc()

else:
    print("⚠️ 项目未成功加载，跳过数据集提取")