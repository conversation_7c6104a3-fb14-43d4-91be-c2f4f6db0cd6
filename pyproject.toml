[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "scape"
version = "1.0.0"
description = "Santos Carbonate Adaptive Permeability Estimator - 巴西桑托斯深水碳酸盐岩渗透率计算框架"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "SCAPE Team", email = "<EMAIL>"}
]
maintainers = [
    {name = "SCAPE Team", email = "<EMAIL>"}
]
keywords = ["petroleum", "carbonate", "permeability", "nmr", "machine-learning", "gpu-computing"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Science/Research",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Scientific/Engineering",
    "Topic :: Scientific/Engineering :: Physics",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
]
requires-python = ">=3.11"

# 核心依赖 - 最小化原则，仅包含必需组件
dependencies = [
    # 核心数据处理
    "pandas>=2.1.0,<3.0.0",
    "numpy>=1.24.0,<2.0.0",
    "scipy>=1.11.0,<2.0.0",

    # Excel文件处理
    "openpyxl>=3.1.0,<4.0.0",

    # 现代化日志与配置 - 基于CCG规范
    "structlog>=23.0.0,<25.0.0",
    "pydantic>=2.5.0,<3.0.0",
    "pydantic-settings>=2.1.0,<3.0.0",

    # 性能监控 - 新增必需依赖
    "psutil>=5.9.0,<6.0.0",

    # 类型安全增强
    "typing-extensions>=4.8.0",

    # 基础工具
    "click>=8.1.0,<9.0.0",
    "tqdm>=4.66.0,<5.0.0",
]

[project.optional-dependencies]
# 机器学习组件 - OBMIQ、SWIFT-PSO算法支持
ml = [
    "scikit-learn>=1.3.0,<2.0.0",
    "xgboost>=2.0.0,<3.0.0",
    "optuna>=3.5.0,<4.0.0",
    "joblib>=1.3.0,<2.0.0",
    # 注意：PyTorch, torchvision, torchaudio 必须根据您的CUDA版本手动安装。
    # 例如，对于CUDA 12.8，请运行以下命令：
    # pip3 install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu128
    # 因此，它们不包含在此依赖组中。
    "tensorboard>=2.10.0",          # PyTorch 可视化工具
    "captum>=0.6.0",                # PyTorch 模型可解释性
    "onnx>=1.14.0",                 # ONNX 模型导出
    "statsmodels>=0.14.0,<1.0.0",   # LogScout VIF计算
]

# GPU计算支持 - 基础GPU库（通用）
gpu = [
    "numba>=0.58.0,<1.0.0",        # JIT编译，CUDA kernel支持
    # 注意：cupy和cudf需要根据CUDA版本手动安装，见下方具体版本组
]

# GPU计算支持 - CUDA 12.x版本
gpu-cuda12 = [
    "cupy-cuda12x>=12.0.0,<14.0.0",
    "numba>=0.58.0,<1.0.0",
    # RAPIDS库 (cuml, cudf) - 推荐使用NVIDIA的索引进行安装
    # pip install --extra-index-url=https://pypi.nvidia.com cuml-cu12 cudf-cu12
    "cuml-cu12>=24.04,<25.0.0",
    "cudf-cu12>=24.04,<25.0.0",
]

# GPU计算支持 - CUDA 11.x版本
gpu-cuda11 = [
    "cupy-cuda11x>=11.0.0,<13.0.0",
    "numba>=0.58.0,<1.0.0",
    # RAPIDS库 (cuml, cudf) - 推荐使用NVIDIA的索引进行安装
    # pip install --extra-index-url=https://pypi.nvidia.com cuml-cu11 cudf-cu11
    "cuml-cu11>=23.10,<24.0.0",
    "cudf-cu11>=23.10,<24.0.0",
]

# 可视化组件 - 数据分析和结果展示
viz = [
    "plotly>=5.17.0,<6.0.0",
    "matplotlib>=3.8.0,<4.0.0",
    "seaborn>=0.13.0,<1.0.0",
]

# 模板系统 - 报告生成和格式化
templates = [
    "jinja2>=3.1.0,<4.0.0",
]

# 开发工具 - 现代化工具栈（基于CCG规范）
dev = [
    # 现代代码质量工具
    "ruff>=0.1.8,<1.0.0",          # 统一linter+formatter，替代black+isort+flake8
    "mypy>=1.8.0,<2.0.0",          # 严格类型检查，支持Protocol和TypedDict

    # 测试框架
    "pytest>=7.4.0,<9.0.0",
    "pytest-cov>=4.1.0,<6.0.0",
    "pytest-xdist>=3.5.0,<4.0.0",
    "pytest-mock>=3.12.0,<4.0.0",  # 模拟和spy功能
    "pytest-asyncio>=0.23.0,<1.0.0",  # 异步测试支持
    "hypothesis>=6.92.0,<7.0.0",   # 属性测试

    # 性能分析和监控
    "memory-profiler>=0.61.0,<1.0.0",
    "line-profiler>=4.1.0,<5.0.0",

    # 开发辅助
    "pre-commit>=3.6.0,<4.0.0",
    "ipython>=8.18.0,<9.0.0",
    "rich>=13.7.0,<14.0.0",        # 美化终端输出
]

# 文档生成
docs = [
    "sphinx>=7.2.0,<8.0.0",
    "sphinx-rtd-theme>=2.0.0,<3.0.0",
    "myst-parser>=2.0.0,<3.0.0",
    "sphinx-autodoc-typehints>=1.25.0,<2.0.0",
]

# Jupyter环境 - 交互式开发和研究
jupyter = [
    "jupyter>=1.0.0,<2.0.0",
    "jupyterlab>=4.0.0,<5.0.0",
    "ipywidgets>=8.1.0,<9.0.0",
    "notebook>=7.0.0,<8.0.0",
]

# 配置文件支持 - TOML/YAML配置加载
config = [
    "toml>=0.10.2,<1.0.0",
    "pyyaml>=6.0.1,<7.0.0",
    "jsonschema>=4.17.0,<5.0.0",
]

# 完整开发环境（不包含GPU，需要根据CUDA版本单独安装）
full = [
    "scape[ml,viz,dev,docs,jupyter,config]"
]

# 完整环境 + CUDA 12.x
full-cuda12 = [
    "scape[ml,gpu-cuda12,viz,dev,docs,jupyter,config]"
]

# 完整环境 + CUDA 11.x
full-cuda11 = [
    "scape[ml,gpu-cuda11,viz,dev,docs,jupyter,config]"
]

# PyTorch CUDA 12.8 安装说明
# ================================
# PyTorch CUDA 12.8 版本需要使用官方提供的特定索引URL进行安装：
#
# 安装命令：
# pip3 install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu128
#
# 或者使用 scape[ml] 安装后，再单独安装 CUDA 12.8 版本：
# pip3 install scape[ml]
# pip3 install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu128 --upgrade
#
# 注意：标准的 pip3 install scape[ml] 可能安装 CPU 版本的 PyTorch，
# 需要使用上述命令确保安装 CUDA 12.8 预编译版本。

[project.urls]
Homepage = "https://github.com/scape-team/scape"
Documentation = "https://scape.readthedocs.io"
Repository = "https://github.com/scape-team/scape"
"Bug Tracker" = "https://github.com/scape-team/scape/issues"
Changelog = "https://github.com/scape-team/scape/blob/main/CHANGELOG.md"

[project.scripts]
scape = "scape_case.cli:main"

# 工具配置
[tool.setuptools.packages.find]
where = ["."]
include = ["logwp*", "scape*", "logwp_extras*", "scape_case*"]
exclude = ["tests*", "docs*", "scripts*", "examples*"]

[tool.setuptools.package-data]
"*" = ["*.yaml", "*.yml", "*.json", "*.toml", "*.md"]

# Ruff配置 - 现代化代码质量工具（基于CCG规范）
[tool.ruff]
target-version = "py311"
line-length = 88
select = [
    "E",   # pycodestyle errors
    "W",   # pycodestyle warnings
    "F",   # pyflakes
    "I",   # isort
    "B",   # flake8-bugbear
    "C4",  # flake8-comprehensions
    "UP",  # pyupgrade
    "RUF", # ruff-specific rules
    "N",   # pep8-naming
    "D",   # pydocstyle
    "ANN", # flake8-annotations
]
ignore = [
    "E501",  # line too long, handled by formatter
    "B008",  # do not perform function calls in argument defaults
    "C901",  # too complex
    "D100",  # missing docstring in public module
    "D104",  # missing docstring in public package
    "ANN101", # missing type annotation for self
    "ANN102", # missing type annotation for cls
]

[tool.ruff.per-file-ignores]
"__init__.py" = ["F401", "D104"]
"tests/**/*" = ["B011", "B018", "RUF012", "D", "ANN"]
"scripts/**/*" = ["D", "ANN"]

[tool.ruff.isort]
known-first-party = ["logwp", "scape", "logwp_extras", "scape_case"]
section-order = ["future", "standard-library", "third-party", "first-party", "local-folder"]

[tool.ruff.pydocstyle]
convention = "google"

# MyPy配置 - 严格类型检查（基于CCG规范）
[tool.mypy]
python_version = "3.11"
strict = true
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
show_error_codes = true
show_column_numbers = true

[[tool.mypy.overrides]]
module = [
    "pandas.*",
    "numpy.*",
    "scipy.*",
    "plotly.*",
    "matplotlib.*",
    "seaborn.*",
    "openpyxl.*",
    "cupy.*",
    "cudf.*",
    "numba.*",
    "sklearn.*",
    "xgboost.*",
    "optuna.*",
    "joblib.*",
    "psutil.*",
    "memory_profiler.*",
    "hypothesis.*",
]
ignore_missing_imports = true

[[tool.mypy.overrides]]
module = ["tests.*", "scripts.*"]
disallow_untyped_defs = false
disallow_incomplete_defs = false

# Pytest配置 - 基于STG测试指南
[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --tb=short --strict-markers --strict-config"
asyncio_mode = "strict" # Replaces deprecated asyncio_default_fixture_loop_scope
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "unit: Unit tests - 快速单元测试",
    "integration: Integration tests - 集成测试",
    "gpu: GPU-accelerated tests - GPU加速测试",
    "slow: Slow tests - 慢速测试",
    "regression: Regression tests - 回归测试",
    "algorithm: Algorithm tests - 算法正确性测试",
    "performance: Performance tests - 性能测试",
]
filterwarnings = [
    "ignore::DeprecationWarning",
    "ignore::PendingDeprecationWarning",
    "ignore::UserWarning:cupy.*",
    "ignore::UserWarning:numba.*",
]

# Coverage配置 - 75%目标覆盖率
[tool.coverage.run]
source = ["logwp", "scape", "logwp_extras", "scape_case"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__init__.py",
    "*/scripts/*",
    "*/examples/*",
]
branch = true
parallel = true

[tool.coverage.report]
precision = 2
show_missing = true
skip_covered = false
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "def __str__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if False:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
    "TYPE_CHECKING",
]

[tool.coverage.html]
directory = "htmlcov"

[tool.coverage.xml]
output = "coverage.xml"
