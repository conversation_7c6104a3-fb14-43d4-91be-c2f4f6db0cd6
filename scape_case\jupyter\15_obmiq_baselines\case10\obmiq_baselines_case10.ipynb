# 导入必要的库
import pandas as pd
import numpy as np
import warnings
from pathlib import Path

# 导入 SCAPE 和 LOGWP 的核心组件
from logwp.io import WpExcelReader
from logwp.extras.tracking import RunContext
from logwp.models.constants import WpDepthRole

# 导入新的OBMIQ Baselines组件
from scape.core.obmiq_baselines import (
    run_obmiq_baselines_training_step,
    run_obmiq_baselines_prediction_step,
    ObmiqBaselinesTrainingConfig,
    ObmiqBaselinesPredictionConfig,
    ObmiqBaselinesTrainingArtifacts,
    ObmiqBaselinesArtifactHandler
)

# 设置
warnings.filterwarnings('ignore')
pd.set_option('display.max_columns', 50)

print("环境设置与导入完成。")

# --- 请在此处填写您的数据路径 ---
# 注意：Baselines组件不使用T2谱序列，但可以使用相同的输入文件
TRAIN_WP_FILE_PATH = "santos_obmiq_cum_all.wp.xlsx"
TRAIN_DATASET_NAME = "nmr_obmiq"

PRED_WP_FILE_PATH = "santos_obmiq_cum_all_apply.wp.xlsx"
PREDICTION_DATASET_NAME = "nmr_obmiq_apply"
# ---------------------------------

try:
    # 获取训练和预测数据束
    reader = WpExcelReader()
    project = reader.read(TRAIN_WP_FILE_PATH)
    train_ds = project.get_dataset(TRAIN_DATASET_NAME)
    train_bundle = train_ds.extract_curve_dataframe_bundle(include_system_columns=True)
    well_no_name = train_bundle.curve_metadata.get_well_identifier_curves()[0]
    depth_name = train_bundle.curve_metadata.get_curves_by_depth_role(WpDepthRole.SINGLE)[0]

    pred_reader = WpExcelReader()
    pred_project = pred_reader.read(PRED_WP_FILE_PATH)
    prediction_ds = pred_project.get_dataset(PREDICTION_DATASET_NAME)
    prediction_bundle = prediction_ds.extract_curve_dataframe_bundle(include_system_columns=True)

    print("数据加载成功。")
    print(f"训练数据束: {train_ds.name}, 形状: {train_ds.df.shape}, 井名: {well_no_name}")
    print(f"预测数据束: {prediction_ds.name}, 形状: {prediction_ds.df.shape}")

except Exception as e:
    print(f"❌ 数据加载失败: {e}")
    project = None
    pred_project = None

if project:
    output_dir = Path("./output01")
    run_dir_name = RunContext.generate_timestamped_run_name(prefix="obmiq_baselines_run")

    with RunContext(run_dir=output_dir / run_dir_name, overwrite=True) as ctx:
        # --- 1. 训练步骤 ---
        print("--- 开始 OBMIQ Baselines 训练步骤 ---")
        training_config = ObmiqBaselinesTrainingConfig(
            n_iter_random_search=20,
            inner_cv_folds=5
        )
        training_kwargs = {
            "tabular_features": ['DEN', 'CN', 'DT', 'RD_LOG10', 'RS_LOG10', 'DRES',
                                 'T2LM_LOG10', 'T2LM_LONG_LOG10', 'T2_P50_LOG10', 'T2_P20_LOG10',
                                 'PHIT_NMR', 'PHIE_NMR', 'BFV_NMR', 'BVI_NMR','FFV_NMR','SWB_NMR', 'SWI_NMR', 'SFF_NMR',
                                 'VMICRO', 'VMESO', 'VMACRO', 'SMICRO', 'SMESO', 'SMACRO',
                                 'LT2STDDEV_FFI', 'LSKEW_FFI', 'LKURT_FFI', 'SDR_PROXY'],
            "target_features": ["DT2_P50", "DPHIT_NMR"],
            "grouping_feature": well_no_name
        }
        training_results = run_obmiq_baselines_training_step(
            config=training_config, ctx=ctx, train_bundle=train_bundle, **training_kwargs
        )
        print("训练步骤完成。")

        # --- 2. 预测步骤 (为每个目标独立调用) ---
        print("\n--- 开始 OBMIQ Baselines 预测步骤 ---")
        handler = ObmiqBaselinesArtifactHandler()
        final_predictions_df = prediction_bundle.data.copy()

        for target_name in training_kwargs["target_features"]:
            print(f"  正在为目标 '{target_name}' 进行预测...")
            model_artifact_name = f"{ObmiqBaselinesTrainingArtifacts.MODEL_ASSETS.value}_{target_name}"
            model_assets_path = ctx.get_artifact_path(model_artifact_name)
            model_assets = handler.load_model_assets(model_assets_path)

            output_curve_name = f"{target_name}_PRED"
            prediction_results = run_obmiq_baselines_prediction_step(
                config=ObmiqBaselinesPredictionConfig(),
                ctx=ctx,
                model_assets=model_assets,
                prediction_bundle=prediction_bundle,
                output_curve_name=output_curve_name
            )
            # 从当次预测结果中读取预测列，并合并到最终结果中
            single_pred_df = pd.read_csv(prediction_results["output_path"])
            final_predictions_df[output_curve_name] = single_pred_df[output_curve_name]

        print("所有目标的预测均已完成。")

        # --- 3. 结果检查 ---
        print("\n最终合并预测结果预览:")
        display(final_predictions_df[["WELL_NO", "MD", "DT2_P50", "DT2_P50_PRED", "DPHIT_NMR", "DPHIT_NMR_PRED"]].head())
else:
    print("因数据加载失败，跳过工作流执行。")