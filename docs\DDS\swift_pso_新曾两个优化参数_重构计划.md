# SWIFT-PSO 新增优化参数重构计划

**版本: 1.0**
**日期: 2024-07-30**
**目标: 将`T2LM_EXP`和`KMACRO_B`从固定值重构为可优化参数**

---

## 1. 目标与背景

当前`scape.core.swift_pso`组件中，FOSTER-NMR物理模型的两个关键指数是固定值：
- `SDR碳酸盐岩渗透率公式`中的`T2LM_EXP`固定为`2.0`。
- `Timur/Coates大孔隙渗透率公式`中的`KMACRO_B`固定为`2.0`。

本次重构的核心目标是将这两个参数从固定值（Hardcoded Value）转变为**可动态优化的模型参数**。这将增强模型的灵活性和拟合能力，使其能够通过数据驱动的方式寻找更符合特定地质条件的最佳指数。

### 1.1. 新参数规格

- **优化范围**: 两个新参数的软硬边界与`PHIT_EXP`参数保持一致，即`(1.0, 1.4, 4.6, 5.0)`。
- **正则化**: 两个新参数将被视为敏感参数，并加入到损失函数的L2正则项中，以防止过拟合。

### 1.2. 核心原则

本次重构将严格遵循**“小步快跑、安全第一”**的敏捷开发策略。每一步修改都应是独立的、可验证的，并有明确的验证方法，以确保整个过程的稳定性和代码质量。

---

## 2. 影响分析摘要

根据调用链分析，本次变更将主要影响以下模块：

1.  **配置层 (`config.py`)**: 需要声明新参数及其边界，这是所有修改的起点。
2.  **物理模型层 (`calculate_foster_nmr.py`)**: 需要修改物理公式，用动态参数替换硬编码值。这是本次重构的核心。
3.  **损失函数层 (`pso_loss_function.py`)**: 需要将新参数纳入L2正则化惩罚。
4.  **文档 (`README.md`, `方法说明书.md`)**: 需要同步更新参数列表和相关描述。

`prediction_facade.py`和`visualization_facade.py`具有良好的设计，能自动适应参数维度的增加，**无需修改代码**。

---

## 3. 详细重构计划 (Step-by-Step Plan)

我们将整个重构过程分解为四个独立的、可依次执行的里程碑。

### 里程碑 1: 配置层更新 (Configuration Layer Update)

**目标**: 让优化器“知道”新参数的存在。

*   **任务 1.1: 修改 `scape/core/swift_pso/config.py`**
    1.  **更新优化参数列表**: 在`SwiftPsoTrainingConfig.optimization_params`字段的`default_factory`中，将`'T2LM_EXP'`和`'KMACRO_B'`添加到默认列表中。使默认优化参数从10个增加到12个。
    2.  **更新参数边界定义**: 在`pso_config_lowo['parameters_boundaries']`和`pso_config_finetune['parameters_boundaries']`两个字典中，为`'T2LM_EXP'`和`'KMACRO_B'`添加新的条目，其值为`(1.0, 1.4, 4.6, 5.0)`。

*   **验证方法**:
    - 编写一个单元测试，实例化`SwiftPsoTrainingConfig.create_default()`。
    - 断言`config.optimization_params`列表的长度为12，并包含`'T2LM_EXP'`和`'KMACRO_B'`。
    - 断言`config.pso_config_lowo['parameters_boundaries']`字典中存在这两个新参数的键，并且其值正确。

---

### 里程碑 2: 物理模型层改造 (Physical Model Layer Refactoring)

**目标**: 改造物理模型计算引擎，使其能够接收并使用动态传入的新参数。这是最关键的一步。

*   **任务 2.1: 修改核心计算引擎 `_calculate_foster_nmr_core`**
    - **文件**: `scape/core/foster_nmr/calculate_foster_nmr.py`
    - **操作**:
        1.  修改`_calculate_foster_nmr_core`函数的签名，增加`T2LM_EXP: float`和`KMACRO_B: float`两个参数。
        2.  移除函数内部的硬编码行：`T2LM_EXP = 2.0`和`KMACRO_B = 2.0`。
        3.  在计算`k_sdr`时，将`** 2.0`替换为`** T2LM_EXP`。
        4.  在计算`k_macro`时，将`** 2.0`替换为`** KMACRO_B`。

*   **任务 2.2: 修改上层调用者以适配新签名**
    - **文件**: `scape/core/foster_nmr/calculate_foster_nmr.py`
    - **操作**:
        1.  **修改 `calculate_foster_nmr_permeability_for_val`**: 在调用`_calculate_foster_nmr_core`时，从输入的`parameters`字典中解包并传递`T2LM_EXP`和`KMACRO_B`。
        2.  **修改 `calculate_foster_nmr_permeability_for_prediction`**: 此函数使用了`**parameters`解包方式。只需确保其调用的`_calculate_foster_nmr_core`函数签名已更新，并且传入的`model_assets['parameters']`未来会包含新参数即可。代码本身可能无需修改，但需要审查。

*   **任务 2.3: 修改向量化损失计算函数 `calculate_foster_nmr_permeability_vectorized_for_loss`**
    - **文件**: `scape/core/foster_nmr/calculate_foster_nmr.py`
    - **操作**:
        1.  在函数内部，从`params`字典中解包出`'T2LM_EXP'`和`'KMACRO_B'`两个新的参数向量。
        2.  在计算`k_sdr`时，将`** 2.0`替换为`** to_row(params['T2LM_EXP'])`。
        3.  在计算`k_macro`时，将`** 2.0`替换为`** to_row(params['KMACRO_B'])`。

*   **验证方法**:
    - 扩展`calculate_foster_nmr.py`的单元测试。
    - 针对`_calculate_foster_nmr_core`，传入不同的`T2LM_EXP`和`KMACRO_B`值（例如1.5和2.5），断言其输出结果与手动计算一致。
    - 针对`calculate_foster_nmr_permeability_vectorized_for_loss`，构造一个包含12个参数的`parameters_matrix`，并验证其输出矩阵的正确性。

---

### 里程碑 3: 损失函数与正则化更新 (Loss Function & Regularization Update)

**目标**: 将新参数纳入L2正则化，防止其在优化过程中出现过拟合。

*   **任务 3.1: 修改 `scape/core/swift_pso/internal/pso_loss_function.py`**
    - **操作**: 在`compute_loss_vectorized`函数内部，找到`sensitive_param_keys`列表，并将`'T2LM_EXP'`和`'KMACRO_B'`添加进去。

*   **验证方法**:
    - 编写或扩展`pso_loss_function.py`的单元测试。
    - 构造一个包含12个优化参数的`parameters_matrix`和`train_data`。
    - 独立计算L2正则项的期望值，并断言`compute_loss_vectorized`函数返回的总损失中，L2部分与期望值一致。

---

### 里程碑 4: 文档同步 (Documentation Sync)

**目标**: 确保所有相关文档与代码变更保持一致，避免误解。

*   **任务 4.1: 更新 `scape/core/swift_pso/README.md`**
    - **操作**:
        1.  将所有“10个参数”的描述更新为“12个参数”。
        2.  在`4.1.1. 【核心功能】灵活的参数优化`一节中，更新`optimization_params`的默认列表。
        3.  在`4.3.1.3 L2正则`的描述中，补充说明`T2LM_EXP`和`KMACRO_B`也被纳入正则化。

*   **任务 4.2: 更新 `docs/SCAPE_方法说明书.md`**
    - **操作**:
        1.  在`3.2 SDR碳酸盐岩渗透率公式`和`3.3 Timur/Coates大孔隙渗透率公式`章节，移除关于`T2LM_EXP`和`KMACRO_B`取固定值`2`的描述。
        2.  在`4.1 待优化参数概况`一节，将两个新参数加入到10个参数的列表中，并更新总数为12个。
        3.  在`4.2.3 参数硬边界和软边界`的表格中，为`T2LM_EXP`和`KMACRO_B`添加新的行，并填入正确的边界值。
        4.  在`4.3.1.3 L2正则`一节，将两个新参数加入到`theta_sensitive`的参数列表中。

*   **验证方法**:
    - 人工审查（Code Review）所有文档的变更，确保其准确性和清晰度。

---

## 4. 整体集成测试

在完成上述所有里程碑后，必须执行一次完整的端到端集成测试。

*   **测试场景**:
    1.  使用默认配置（即优化全部12个参数）运行`training_facade.run_swift_pso_training_step`。
    2.  断言训练步骤成功完成，并检查其产物`FINAL_PARAMETERS`是否包含12个参数及其优化值。
    3.  使用训练产出的模型资产，调用`prediction_facade.run_swift_pso_prediction_step`，断言预测步骤成功。
    4.  使用训练产出的`ALL_OPTIMIZED_PARAMETERS`数据，调用`visualization_facade.run_tsne_visualization_step`，断言可视化步骤成功，并人工检查生成的图表和报告是否包含了12个参数的分析。

*   **目的**: 验证所有修改过的部分能够无缝协同工作，确保整个`swift_pso`组件在重构后功能正确、表现稳定。

